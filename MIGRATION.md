# Guia de Migração - QR Code Styling v2.0

## Visão Geral

Este guia ajuda na migração do QR Code Styling v1.x para v2.0, que inclui uma refatoração completa com princípios SOLID e integração PIX.

## Mudanças Principais

### 1. Nova API Unificada

**v1.x (Antigo):**
```typescript
import QRCodeStyling from 'qr-code-styling';

const qr = new QRCodeStyling({
  width: 300,
  height: 300,
  data: "https://example.com",
  dotsOptions: {
    color: "#4267b2",
    type: "rounded"
  }
});
```

**v2.0 (Novo - Recomendado):**
```typescript
import { UnifiedQRCode } from 'qr-code-styling';

const qr = new UnifiedQRCode({
  width: 300,
  height: 300,
  data: "https://example.com",
  dotsOptions: {
    color: "#4267b2",
    type: "rounded"
  }
});
```

**v2.0 (Compatibilidade):**
```typescript
// Ainda funciona - mantém compatibilidade
import QRCodeStyling from 'qr-code-styling';

const qr = new QRCodeStyling({
  width: 300,
  height: 300,
  data: "https://example.com",
  dotsOptions: {
    color: "#4267b2",
    type: "rounded"
  }
});
```

### 2. Funcionalidades PIX (Novo)

```typescript
import { createPixQR } from 'qr-code-styling';

// Criar QR PIX
const pixQR = createPixQR({
  key: "<EMAIL>",
  nameReceiver: "João Silva",
  cityReceiver: "São Paulo",
  amount: 25.50,
  description: "Pagamento de serviço"
});

// Ou usando a classe principal
const qr = new UnifiedQRCode({
  pix: {
    key: "11999999999",
    nameReceiver: "Maria Santos",
    cityReceiver: "Rio de Janeiro",
    amount: 100.00
  }
});
```

### 3. Estilo Avançado (Novo)

```typescript
import { createStyledQR } from 'qr-code-styling';

const styledQR = createStyledQR("https://example.com", {
  markerStyle: 'rounded',
  borderStyle: 'circle',
  gradientColor: '#FF6B6B',
  gradientMode: 'gradient',
  frameStyle: 'tech',
  centerImage: './logo.png',
  centerImageSize: 0.3
});
```

## Migração Automática

### Helper de Migração

```typescript
import { Migration } from 'qr-code-styling';

// Verificar se migração é necessária
const migrationInfo = Migration.checkMigrationNeeded(oldOptions);
if (migrationInfo.needed) {
  console.log(`Migration needed: ${migrationInfo.type}`);
  console.log('Suggestions:', migrationInfo.suggestions);
}

// Migrar automaticamente
const newOptions = Migration.fromQRCodeStyling(oldOptions);
const qr = new UnifiedQRCode(newOptions);
```

### Migração de qr-pix

```typescript
// Se você usava qr-pix separadamente
const oldPixOptions = {
  data: brCode,
  boxSize: 10,
  border: 4,
  markerStyle: 'rounded',
  gradientColor: '#FF0000'
};

// Migrar para nova API
const newOptions = Migration.fromQRPix(oldPixOptions);
const qr = new UnifiedQRCode(newOptions);
```

## Mudanças de API

### Métodos Mantidos (Compatíveis)

```typescript
// Estes métodos continuam funcionando igual
qr.update(options);
qr.append(container);
qr.download(downloadOptions);
qr.getRawData(extension);
```

### Novos Métodos

```typescript
// Funcionalidades PIX
qr.generatePix(pixData);
qr.parseBRCode(brCode);
qr.validatePixKey(key);

// Estilo avançado
qr.applyAdvancedStyle(styleOptions);
qr.setTheme('dark');
qr.addFrame('tech');
qr.addCenterImage('./logo.png', 0.25);

// Export avançado
qr.exportAs('png', { quality: 0.9 });
qr.toBase64('jpeg');
qr.saveToFile('./qr.png');
```

## Exemplos de Migração

### Exemplo 1: QR Básico

**Antes (v1.x):**
```typescript
import QRCodeStyling from 'qr-code-styling';

const qr = new QRCodeStyling({
  width: 300,
  height: 300,
  type: "svg",
  data: "https://www.facebook.com/",
  image: "https://upload.wikimedia.org/wikipedia/commons/5/51/Facebook_f_logo_%282019%29.svg",
  dotsOptions: {
    color: "#4267b2",
    type: "rounded"
  },
  backgroundOptions: {
    color: "#e9ebee",
  },
  imageOptions: {
    crossOrigin: "anonymous",
    margin: 20
  }
});

document.getElementById("canvas").appendChild(qr.getElement());
qr.download({ name: "qr", extension: "svg" });
```

**Depois (v2.0):**
```typescript
import { UnifiedQRCode } from 'qr-code-styling';

const qr = new UnifiedQRCode({
  width: 300,
  height: 300,
  type: "svg",
  data: "https://www.facebook.com/",
  image: "https://upload.wikimedia.org/wikipedia/commons/5/51/Facebook_f_logo_%282019%29.svg",
  dotsOptions: {
    color: "#4267b2",
    type: "rounded"
  },
  backgroundOptions: {
    color: "#e9ebee",
  },
  imageOptions: {
    crossOrigin: "anonymous",
    margin: 20
  }
});

qr.append(document.getElementById("canvas"));
qr.download({ name: "qr", extension: "svg" });
```

### Exemplo 2: QR PIX

**Novo em v2.0:**
```typescript
import { createPixQR } from 'qr-code-styling';

const pixQR = createPixQR({
  key: "<EMAIL>",
  nameReceiver: "Loja ABC",
  cityReceiver: "São Paulo",
  amount: 50.00,
  description: "Compra online"
}, {
  width: 300,
  height: 300,
  dotsOptions: {
    color: "#00875F",
    type: "rounded"
  },
  backgroundOptions: {
    color: "#FFFFFF"
  },
  advancedStyling: {
    frameStyle: 'pay',
    markerStyle: 'rounded'
  }
});

pixQR.append(document.getElementById("pix-container"));
```

## Checklist de Migração

### ✅ Preparação
- [ ] Backup do código atual
- [ ] Instalar nova versão: `npm install qr-code-styling@2.0.0`
- [ ] Revisar dependências

### ✅ Código
- [ ] Atualizar imports se necessário
- [ ] Testar funcionalidades existentes
- [ ] Verificar se há warnings de deprecação
- [ ] Executar helper de migração se necessário

### ✅ Funcionalidades Novas (Opcional)
- [ ] Implementar funcionalidades PIX se aplicável
- [ ] Explorar novos estilos avançados
- [ ] Testar novos métodos de export

### ✅ Testes
- [ ] Executar testes existentes
- [ ] Testar em diferentes navegadores
- [ ] Verificar performance

## Problemas Comuns

### 1. Import Errors

**Problema:**
```
Module not found: Can't resolve 'qr-code-styling/UnifiedQRCode'
```

**Solução:**
```typescript
// Correto
import { UnifiedQRCode } from 'qr-code-styling';

// Ou use o default (compatibilidade)
import QRCodeStyling from 'qr-code-styling';
```

### 2. TypeScript Errors

**Problema:**
```
Property 'pix' does not exist on type 'Options'
```

**Solução:**
```typescript
// Use os novos tipos
import { UnifiedOptions } from 'qr-code-styling';

const options: UnifiedOptions = {
  data: "test",
  pix: { /* ... */ }
};
```

### 3. Funcionalidades PIX não funcionam

**Problema:** Métodos PIX retornam erro

**Solução:**
```typescript
// Habilitar funcionalidades PIX
const qr = new UnifiedQRCode({
  data: "test",
  features: {
    enablePix: true
  }
});
```

## Suporte

### Documentação
- [API Reference](./docs/api.md)
- [Examples](./examples/)
- [TypeScript Definitions](./src/types/)

### Comunidade
- [GitHub Issues](https://github.com/kozakdenys/qr-code-styling/issues)
- [Discussions](https://github.com/kozakdenys/qr-code-styling/discussions)

### Versioning
- v2.0.x: Nova arquitetura com PIX
- v1.6.x: Versão legacy (manutenção apenas)

## Conclusão

A migração para v2.0 é opcional mas recomendada para aproveitar:
- ✨ Funcionalidades PIX nativas
- 🎨 Estilos avançados
- 🏗️ Arquitetura mais robusta
- 📦 Melhor TypeScript support
- 🚀 Performance otimizada

A compatibilidade com v1.x garante que a migração pode ser gradual e sem pressa.
