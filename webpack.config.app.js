const { merge } = require("webpack-merge");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const path = require("path");
const commonConfig = require("./webpack.config.common.js");

const rootPath = path.resolve(__dirname, "./");
const srcPath = path.resolve(rootPath, "src");

module.exports = merge(commonConfig, {
  mode: "development",
  entry: {
    // Main application entry point
    app: srcPath + "/frontend/main.ts",
    // Library entry point for backward compatibility
    lib: srcPath + "/index.ts"
  },
  output: {
    path: path.resolve(__dirname, "dist"),
    filename: "[name].bundle.js",
    clean: true,
    globalObject: "this",
    library: {
      name: "PixQRApp",
      type: "umd",
      export: "default"
    }
  },
  devServer: {
    historyApiFallback: {
      rewrites: [{ from: /^\/$/, to: "/index.html" }]
    },
    static: [
      {
        directory: path.join(__dirname, "public")
      }
    ],
    port: 8080,
    open: true,
    hot: true,
    compress: true
  },
  devtool: "inline-source-map",
  plugins: [
    new HtmlWebpackPlugin({
      template: "./public/index.html",
      filename: "index.html",
      inject: "head",
      scriptLoading: "blocking",
      chunks: ["app"] // Only include the app bundle in HTML
    })
  ],
  resolve: {
    extensions: [".ts", ".js"],
    alias: {
      "@": srcPath,
      "@frontend": path.resolve(srcPath, "frontend"),
      "@core": path.resolve(srcPath, "core"),
      "@pix": path.resolve(srcPath, "pix"),
      "@types": path.resolve(srcPath, "types"),
      "@utils": path.resolve(srcPath, "utils")
    }
  },
  optimization: {
    splitChunks: {
      chunks: "all",
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          chunks: "all"
        },
        common: {
          name: "common",
          minChunks: 2,
          chunks: "all",
          enforce: true
        }
      }
    }
  }
});
