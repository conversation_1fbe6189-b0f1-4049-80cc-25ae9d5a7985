/**
 * Style-related types and interfaces
 * Unified styling system for QR codes
 */

/**
 * Base style configuration
 */
export interface BaseStyleConfig {
  color?: string;
  gradient?: GradientStyleConfig;
}

/**
 * Gradient style configuration
 */
export interface GradientStyleConfig {
  type: 'linear' | 'radial';
  rotation?: number;
  colorStops: ColorStop[];
}

/**
 * Color stop for gradients
 */
export interface ColorStop {
  offset: number;
  color: string;
}

/**
 * Dot style configuration
 */
export interface DotStyleConfig extends BaseStyleConfig {
  type: DotStyleType;
  roundSize?: boolean;
  size?: number;
}

/**
 * Corner style configuration
 */
export interface CornerStyleConfig extends BaseStyleConfig {
  type: CornerStyleType;
  size?: number;
}

/**
 * Background style configuration
 */
export interface BackgroundStyleConfig extends BaseStyleConfig {
  round?: number;
  opacity?: number;
}

/**
 * Dot style types
 */
export enum DotStyleType {
  SQUARE = 'square',
  DOTS = 'dots',
  ROUNDED = 'rounded',
  CLASSY = 'classy',
  CLASSY_ROUNDED = 'classy-rounded',
  EXTRA_ROUNDED = 'extra-rounded'
}

/**
 * Corner style types
 */
export enum CornerStyleType {
  SQUARE = 'square',
  DOT = 'dot',
  ROUNDED = 'rounded',
  EXTRA_ROUNDED = 'extra-rounded'
}

/**
 * Advanced marker styles (from PIX)
 */
export enum AdvancedMarkerStyle {
  SQUARE = 'square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  QUARTER_CIRCLE = 'quarter_circle',
  STAR = 'star',
  DIAMOND = 'diamond',
  PLUS = 'plus'
}

/**
 * Border styles
 */
export enum BorderStyle {
  SQUARE = 'square',
  ROUNDED = 'rounded',
  CIRCLE = 'circle'
}

/**
 * Line styles
 */
export enum LineStyle {
  SQUARE = 'square',
  GAPPED_SQUARE = 'gapped_square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  VERTICAL_BARS = 'vertical_bars',
  HORIZONTAL_BARS = 'horizontal_bars'
}

/**
 * Frame styles
 */
export enum FrameStyle {
  NONE = 'none',
  CLEAN = 'clean',
  TECH = 'tech',
  CREATIVE = 'creative',
  PAY = 'pay',
  SCAN_ME_PURPLE = 'scan_me_purple',
  SCAN_ME_NEON = 'scan_me_neon',
  SCAN_ME_TECH = 'scan_me_tech'
}

/**
 * Style application mode
 */
export enum StyleMode {
  NORMAL = 'normal',
  FULL = 'full',
  MINIMAL = 'minimal'
}

/**
 * Style theme configuration
 */
export interface StyleTheme {
  name: string;
  dotStyle: DotStyleConfig;
  cornerStyle: CornerStyleConfig;
  backgroundStyle: BackgroundStyleConfig;
  advancedOptions?: {
    markerStyle?: AdvancedMarkerStyle;
    borderStyle?: BorderStyle;
    lineStyle?: LineStyle;
    frameStyle?: FrameStyle;
  };
}

/**
 * Style preset definitions
 */
export interface StylePreset {
  id: string;
  name: string;
  description: string;
  theme: StyleTheme;
  preview?: string;
}

/**
 * Style animation configuration
 */
export interface StyleAnimation {
  type: 'fade' | 'slide' | 'rotate' | 'scale';
  duration: number;
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
  repeat?: number | 'infinite';
}

/**
 * Style transformation configuration
 */
export interface StyleTransform {
  scale?: number;
  rotate?: number;
  translateX?: number;
  translateY?: number;
  skewX?: number;
  skewY?: number;
}

/**
 * Style filter configuration
 */
export interface StyleFilter {
  blur?: number;
  brightness?: number;
  contrast?: number;
  saturate?: number;
  hueRotate?: number;
  opacity?: number;
}

/**
 * Complete style configuration
 */
export interface CompleteStyleConfig {
  dots: DotStyleConfig;
  corners: CornerStyleConfig;
  background: BackgroundStyleConfig;
  advanced?: {
    marker?: AdvancedMarkerStyle;
    border?: BorderStyle;
    line?: LineStyle;
    frame?: FrameStyle;
    mode?: StyleMode;
  };
  animation?: StyleAnimation;
  transform?: StyleTransform;
  filter?: StyleFilter;
}
