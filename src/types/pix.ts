/**
 * PIX-specific types and interfaces
 * Unified from qr-pix functionality
 */

/**
 * PIX data interface for BR-Code generation
 */
export interface IPixData {
  key: string;
  nameReceiver: string;
  cityReceiver: string;
  amount?: number;
  zipcodeReceiver?: string;
  identification?: string;
  description?: string;
  defaultUrlPix?: string;
  singleTransaction?: boolean;
}

/**
 * PIX configuration interface
 */
export interface PixConfig {
  key?: string;
  nameReceiver?: string;
  cityReceiver?: string;
  amount?: number;
  zipcodeReceiver?: string;
  identification?: string;
  description?: string;
  defaultUrlPix?: string;
  singleTransaction?: boolean;
}

/**
 * PIX key types
 */
export enum PixKeyType {
  CPF = 'cpf',
  CNPJ = 'cnpj',
  EMAIL = 'email',
  PHONE = 'phone',
  RANDOM = 'random'
}

/**
 * BR-Code parsed data
 */
export interface ParsedBRCode {
  nome?: string;
  cidade?: string;
  valor?: number;
  chave?: string;
  txid?: string;
  cep?: string;
  descricao?: string;
  urlPix?: string;
  [key: string]: any;
}

/**
 * PIX transaction types
 */
export enum PixTransactionType {
  STATIC = 'static',
  DYNAMIC = 'dynamic'
}

/**
 * PIX validation result
 */
export interface PixValidationResult {
  isValid: boolean;
  errors: string[];
  keyType?: PixKeyType;
}

/**
 * PIX QR Code generation options
 */
export interface PixQROptions {
  data?: string;
  output?: string;
  boxSize?: number;
  border?: number;
  customLogo?: string;
  markerStyle?: string;
  borderStyle?: string;
  lineStyle?: string;
  gradientColor?: string;
  gradientMode?: string;
  frameStyle?: string;
  styleMode?: 'Normal' | 'Full';
}

/**
 * BR-Code field identifiers
 */
export enum BRCodeFields {
  PAYLOAD_FORMAT_INDICATOR = '00',
  POINT_OF_INITIATION_METHOD = '01',
  MERCHANT_ACCOUNT_INFORMATION = '26',
  MERCHANT_CATEGORY_CODE = '52',
  TRANSACTION_CURRENCY = '53',
  TRANSACTION_AMOUNT = '54',
  COUNTRY_CODE = '58',
  MERCHANT_NAME = '59',
  MERCHANT_CITY = '60',
  POSTAL_CODE = '61',
  ADDITIONAL_DATA_FIELD = '62',
  CRC16 = '63'
}

/**
 * PIX merchant account information sub-fields
 */
export enum PixMerchantFields {
  GUI = '00',
  PIX_KEY = '01',
  DESCRIPTION = '02',
  URL = '25'
}

/**
 * PIX additional data fields
 */
export enum PixAdditionalFields {
  TXID = '05',
  REFERENCE_LABEL = '25'
}

/**
 * PIX error codes
 */
export enum PixErrorCode {
  INVALID_KEY = 'INVALID_KEY',
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  INVALID_NAME = 'INVALID_NAME',
  INVALID_CITY = 'INVALID_CITY',
  INVALID_ZIPCODE = 'INVALID_ZIPCODE',
  INVALID_DESCRIPTION = 'INVALID_DESCRIPTION',
  INVALID_BRCODE = 'INVALID_BRCODE',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD'
}

/**
 * PIX validation rules
 */
export interface PixValidationRules {
  key: {
    required: boolean;
    maxLength: number;
    pattern?: RegExp;
  };
  nameReceiver: {
    required: boolean;
    maxLength: number;
    minLength: number;
  };
  cityReceiver: {
    required: boolean;
    maxLength: number;
    minLength: number;
  };
  amount: {
    required: boolean;
    min: number;
    max: number;
  };
  description: {
    required: boolean;
    maxLength: number;
  };
}

/**
 * PIX service provider information
 */
export interface PixServiceProvider {
  name: string;
  code: string;
  url?: string;
  supportsDynamic: boolean;
  supportsStatic: boolean;
}

/**
 * PIX QR Code metadata
 */
export interface PixQRMetadata {
  version: string;
  generatedAt: Date;
  expiresAt?: Date;
  transactionType: PixTransactionType;
  keyType: PixKeyType;
  amount?: number;
  currency: string;
  countryCode: string;
}
