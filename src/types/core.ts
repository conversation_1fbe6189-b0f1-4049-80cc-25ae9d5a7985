import { JSDOM } from "jsdom";
import nodeCanvas from "canvas";

// Re-export existing types for compatibility
export * from "./index";
export * from "./pix";
export * from "./styles";

/**
 * Unified configuration for QR Code generation and styling
 * Combines functionality from both qr-code-styling and qr-pix
 */
export interface UnifiedQROptions {
  // Core QR options
  type?: 'canvas' | 'svg';
  shape?: 'square' | 'circle';
  width?: number;
  height?: number;
  margin?: number;
  data?: string;
  
  // QR generation options
  qrOptions?: {
    typeNumber?: number;
    mode?: 'Numeric' | 'Alphanumeric' | 'Byte' | 'Kanji';
    errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
  };

  // Image options
  imageOptions?: {
    saveAsBlob?: boolean;
    hideBackgroundDots?: boolean;
    imageSize?: number;
    crossOrigin?: string;
    margin?: number;
  };

  // Style options
  dotsOptions?: {
    type?: string;
    color?: string;
    gradient?: GradientConfig;
    roundSize?: boolean;
  };

  cornersSquareOptions?: {
    type?: string;
    color?: string;
    gradient?: GradientConfig;
  };

  cornersDotOptions?: {
    type?: string;
    color?: string;
    gradient?: GradientConfig;
  };

  backgroundOptions?: {
    round?: number;
    color?: string;
    gradient?: GradientConfig;
  };

  // PIX-specific options
  pixOptions?: {
    key?: string;
    nameReceiver?: string;
    cityReceiver?: string;
    amount?: number;
    zipcodeReceiver?: string;
    identification?: string;
    description?: string;
    defaultUrlPix?: string;
    singleTransaction?: boolean;
  };

  // Advanced styling (from qr-pix)
  advancedStyling?: {
    markerStyle?: MarkerStyleType;
    borderStyle?: BorderStyleType;
    lineStyle?: LineStyleType;
    gradientColor?: string;
    gradientMode?: GradientModeType;
    frameStyle?: FrameStyleType;
    styleMode?: 'Normal' | 'Full';
    customLogo?: string;
  };

  // Environment options
  nodeCanvas?: typeof nodeCanvas;
  jsdom?: typeof JSDOM;
}

/**
 * Gradient configuration
 */
export interface GradientConfig {
  type: 'radial' | 'linear';
  rotation?: number;
  colorStops: {
    offset: number;
    color: string;
  }[];
}

/**
 * Marker style types (from qr-pix)
 */
export enum MarkerStyleType {
  SQUARE = 'square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  QUARTER_CIRCLE = 'quarter_circle',
  STAR = 'star',
  DIAMOND = 'diamond',
  PLUS = 'plus'
}

/**
 * Border style types
 */
export enum BorderStyleType {
  SQUARE = 'square',
  ROUNDED = 'rounded',
  CIRCLE = 'circle'
}

/**
 * Line style types
 */
export enum LineStyleType {
  SQUARE = 'square',
  GAPPED_SQUARE = 'gapped_square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  VERTICAL_BARS = 'vertical_bars',
  HORIZONTAL_BARS = 'horizontal_bars'
}

/**
 * Frame style types
 */
export enum FrameStyleType {
  CLEAN = 'clean',
  TECH = 'tech',
  CREATIVE = 'creative',
  PAY = 'pay',
  SCAN_ME_PURPLE = 'scan_me_purple',
  SCAN_ME_NEON = 'scan_me_neon',
  SCAN_ME_TECH = 'scan_me_tech'
}

/**
 * Gradient mode types
 */
export enum GradientModeType {
  NORMAL = 'normal',
  GRADIENT = 'gradient',
  MULTI = 'multi'
}

/**
 * Export options for different formats
 */
export interface ExportOptions {
  format: 'png' | 'jpeg' | 'svg' | 'webp';
  quality?: number;
  filename?: string;
  base64?: boolean;
}

/**
 * Rendering context information
 */
export interface RenderingContext {
  width: number;
  height: number;
  moduleCount: number;
  moduleSize: number;
  margin: number;
  qrData: any;
}

/**
 * Style application context
 */
export interface StyleContext {
  element: any;
  position: { x: number; y: number };
  size: number;
  moduleSize: number;
  isCorner: boolean;
  isCornerDot: boolean;
  neighbors: {
    top: boolean;
    right: boolean;
    bottom: boolean;
    left: boolean;
  };
}
