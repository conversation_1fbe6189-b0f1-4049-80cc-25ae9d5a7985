/**
 * Compatibility layer for unified QR code functionality
 * Bridges qr-code-styling and qr-pix APIs
 */

import { Options as QRCodeStylingOptions } from "./index";
import { IPixData, PixQROptions } from "./pix";
import { CompleteStyleConfig } from "./styles";

/**
 * Unified API that combines both libraries
 */
export interface UnifiedQRCodeAPI {
  // Standard QR Code methods (from qr-code-styling)
  update(options?: Partial<UnifiedOptions>): void;
  append(container?: HTMLElement): void;
  download(downloadOptions?: DownloadOptions): Promise<void>;
  getRawData(extension?: FileExtension): Promise<Blob | Buffer | null>;
  
  // PIX-specific methods (from qr-pix)
  generatePix(pixData: IPixData): string;
  parseBRCode(brCode: string): IPixData;
  validatePixKey(key: string): boolean;
  
  // Advanced styling methods
  applyAdvancedStyle(style: AdvancedStyleOptions): void;
  setTheme(theme: string): void;
  addFrame(frameStyle: string): void;
  addCenterImage(imagePath: string, size?: number): Promise<void>;
  
  // Export methods
  exportAs(format: ExportFormat, options?: ExportOptions): Promise<Buffer | string>;
  toBase64(format?: string): Promise<string>;
  saveToFile(path: string, format?: string): Promise<void>;
}

/**
 * Unified options that work with both libraries
 */
export interface UnifiedOptions extends QRCodeStylingOptions {
  // PIX-specific options
  pix?: IPixData;
  
  // Advanced styling from qr-pix
  advancedStyling?: AdvancedStyleOptions;
  
  // Complete style configuration
  styles?: CompleteStyleConfig;
  
  // Export configuration
  export?: {
    format?: ExportFormat;
    quality?: number;
    filename?: string;
    base64?: boolean;
  };
  
  // Performance options
  performance?: {
    enableCaching?: boolean;
    maxCacheSize?: number;
    enableWorkers?: boolean;
  };
  
  // Feature flags
  features?: {
    enablePix?: boolean;
    enableAdvancedStyling?: boolean;
    enableAnimations?: boolean;
    strictMode?: boolean;
  };
}

/**
 * Advanced styling options from qr-pix
 */
export interface AdvancedStyleOptions {
  markerStyle?: MarkerStyle;
  borderStyle?: BorderStyle;
  lineStyle?: LineStyle;
  gradientColor?: string;
  gradientMode?: GradientMode;
  frameStyle?: FrameStyle;
  styleMode?: 'Normal' | 'Full';
  centerImage?: string;
  centerImageSize?: number;
}

/**
 * Export formats supported by unified API
 */
export type ExportFormat = 'png' | 'jpeg' | 'svg' | 'webp' | 'gif' | 'pdf';

/**
 * File extensions
 */
export type FileExtension = 'png' | 'jpeg' | 'jpg' | 'svg' | 'webp' | 'gif' | 'pdf';

/**
 * Download options
 */
export interface DownloadOptions {
  name?: string;
  extension?: FileExtension;
}

/**
 * Export options
 */
export interface ExportOptions {
  quality?: number;
  width?: number;
  height?: number;
  background?: string;
  progressive?: boolean;
  metadata?: Record<string, any>;
}

/**
 * Marker styles
 */
export enum MarkerStyle {
  SQUARE = 'square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  QUARTER_CIRCLE = 'quarter_circle',
  STAR = 'star',
  DIAMOND = 'diamond',
  PLUS = 'plus'
}

/**
 * Border styles
 */
export enum BorderStyle {
  SQUARE = 'square',
  ROUNDED = 'rounded',
  CIRCLE = 'circle'
}

/**
 * Line styles
 */
export enum LineStyle {
  SQUARE = 'square',
  GAPPED_SQUARE = 'gapped_square',
  CIRCLE = 'circle',
  ROUNDED = 'rounded',
  VERTICAL_BARS = 'vertical_bars',
  HORIZONTAL_BARS = 'horizontal_bars'
}

/**
 * Gradient modes
 */
export enum GradientMode {
  NORMAL = 'normal',
  GRADIENT = 'gradient',
  MULTI = 'multi'
}

/**
 * Frame styles
 */
export enum FrameStyle {
  NONE = 'none',
  CLEAN = 'clean',
  TECH = 'tech',
  CREATIVE = 'creative',
  PAY = 'pay',
  SCAN_ME_PURPLE = 'scan_me_purple',
  SCAN_ME_NEON = 'scan_me_neon',
  SCAN_ME_TECH = 'scan_me_tech'
}

/**
 * Migration helper for converting old options to unified format
 */
export class OptionsConverter {
  /**
   * Converts qr-code-styling options to unified format
   * @param options Original qr-code-styling options
   * @returns Unified options
   */
  static fromQRCodeStyling(options: QRCodeStylingOptions): UnifiedOptions {
    return {
      ...options,
      features: {
        enablePix: false,
        enableAdvancedStyling: false,
        enableAnimations: false,
        strictMode: false
      }
    };
  }

  /**
   * Converts qr-pix options to unified format
   * @param options Original qr-pix options
   * @returns Unified options
   */
  static fromQRPix(options: PixQROptions): UnifiedOptions {
    return {
      width: (options.boxSize || 10) * 25,
      height: (options.boxSize || 10) * 25,
      margin: options.border || 4,
      data: options.data || '',
      advancedStyling: {
        markerStyle: options.markerStyle as MarkerStyle,
        borderStyle: options.borderStyle as BorderStyle,
        lineStyle: options.lineStyle as LineStyle,
        gradientColor: options.gradientColor,
        gradientMode: options.gradientMode as GradientMode,
        frameStyle: options.frameStyle as FrameStyle,
        styleMode: options.styleMode,
        centerImage: options.customLogo
      },
      features: {
        enablePix: true,
        enableAdvancedStyling: true,
        enableAnimations: false,
        strictMode: false
      }
    };
  }

  /**
   * Converts unified options back to qr-code-styling format
   * @param options Unified options
   * @returns qr-code-styling options
   */
  static toQRCodeStyling(options: UnifiedOptions): QRCodeStylingOptions {
    const { pix, advancedStyling, styles, export: exportOptions, performance, features, ...qrOptions } = options;
    return qrOptions;
  }

  /**
   * Converts unified options back to qr-pix format
   * @param options Unified options
   * @returns qr-pix options
   */
  static toQRPix(options: UnifiedOptions): PixQROptions {
    return {
      data: options.data,
      boxSize: Math.floor((options.width || 250) / 25),
      border: options.margin,
      customLogo: options.advancedStyling?.centerImage,
      markerStyle: options.advancedStyling?.markerStyle,
      borderStyle: options.advancedStyling?.borderStyle,
      lineStyle: options.advancedStyling?.lineStyle,
      gradientColor: options.advancedStyling?.gradientColor,
      gradientMode: options.advancedStyling?.gradientMode,
      frameStyle: options.advancedStyling?.frameStyle,
      styleMode: options.advancedStyling?.styleMode
    };
  }
}

/**
 * Version information for compatibility tracking
 */
export interface VersionInfo {
  unified: string;
  qrCodeStyling: string;
  qrPix: string;
  breaking: boolean;
  migrationRequired: boolean;
}

/**
 * Feature detection utilities
 */
export class FeatureDetector {
  /**
   * Checks if PIX features are available
   * @returns true if PIX features are supported
   */
  static hasPixSupport(): boolean {
    return typeof require !== 'undefined';
  }

  /**
   * Checks if advanced styling is available
   * @returns true if advanced styling is supported
   */
  static hasAdvancedStyling(): boolean {
    return true; // Always available in unified version
  }

  /**
   * Checks if image processing is available
   * @returns true if image processing is supported
   */
  static hasImageProcessing(): boolean {
    try {
      require('sharp');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Gets available export formats
   * @returns Array of supported export formats
   */
  static getAvailableExportFormats(): ExportFormat[] {
    const baseFormats: ExportFormat[] = ['png', 'jpeg', 'svg'];
    
    if (this.hasImageProcessing()) {
      baseFormats.push('webp', 'gif', 'pdf');
    }
    
    return baseFormats;
  }
}
