/**
 * Unified types that bridge qr-code-styling and qr-pix functionality
 * Maintains backward compatibility while enabling new features
 */

import { Options as OriginalOptions } from "./index";
import { PixConfig, PixQROptions } from "./pix";
import { CompleteStyleConfig } from "./styles";

/**
 * Unified options that combine both libraries' capabilities
 * Maintains backward compatibility with existing APIs
 */
export interface UnifiedOptions extends OriginalOptions {
  // PIX-specific configuration
  pix?: PixConfig;
  
  // Advanced styling from qr-pix
  advancedStyling?: {
    markerStyle?: string;
    borderStyle?: string;
    lineStyle?: string;
    gradientColor?: string;
    gradientMode?: string;
    frameStyle?: string;
    styleMode?: 'Normal' | 'Full';
    customLogo?: string;
  };

  // Complete style configuration
  styles?: CompleteStyleConfig;

  // Export options
  export?: {
    format?: 'png' | 'jpeg' | 'svg' | 'webp';
    quality?: number;
    filename?: string;
    base64?: boolean;
  };
}

/**
 * Unified QR Code instance that supports both standard and PIX functionality
 */
export interface UnifiedQRCode {
  // Standard QR Code methods
  update(options?: Partial<UnifiedOptions>): void;
  append(container?: HTMLElement): void;
  download(downloadOptions?: { name?: string; extension?: string }): Promise<void>;
  getRawData(extension?: string): Promise<Uint8Array>;
  
  // PIX-specific methods
  generateBRCode(): string;
  setPixData(pixData: PixConfig): void;
  validatePixKey(key: string): boolean;
  
  // Advanced styling methods
  applyTheme(theme: string): void;
  setAdvancedStyle(style: any): void;
  
  // Export methods
  exportAs(format: string, options?: any): Promise<Buffer | string>;
  toBase64(format?: string): Promise<string>;
  saveToFile(path: string, format?: string): Promise<void>;
}

/**
 * Factory configuration for creating unified instances
 */
export interface UnifiedFactoryConfig {
  defaultRenderer: 'canvas' | 'svg';
  enablePix: boolean;
  enableAdvancedStyling: boolean;
  defaultOptions: Partial<UnifiedOptions>;
}

/**
 * Migration helper types for backward compatibility
 */
export interface LegacyOptionsMap {
  // Map old qr-code-styling options to new unified structure
  'qr-code-styling': Partial<OriginalOptions>;
  
  // Map old qr-pix options to new unified structure
  'qr-pix': PixQROptions;
}

/**
 * Version compatibility information
 */
export interface VersionInfo {
  unified: string;
  qrCodeStyling: string;
  qrPix: string;
  breaking: boolean;
  migrationRequired: boolean;
}

/**
 * Feature flags for enabling/disabling functionality
 */
export interface FeatureFlags {
  enablePix: boolean;
  enableAdvancedStyling: boolean;
  enableAnimations: boolean;
  enableWebGL: boolean;
  enableWorkers: boolean;
  strictMode: boolean;
}

/**
 * Performance monitoring configuration
 */
export interface PerformanceConfig {
  enableProfiling: boolean;
  maxRenderTime: number;
  memoryLimit: number;
  cacheSize: number;
}

/**
 * Plugin system interface
 */
export interface PluginInterface {
  name: string;
  version: string;
  init(api: UnifiedQRCode): void;
  destroy(): void;
}

/**
 * Event system for QR Code lifecycle
 */
export interface QRCodeEvents {
  'beforeRender': (options: UnifiedOptions) => void;
  'afterRender': (element: any) => void;
  'beforeExport': (format: string) => void;
  'afterExport': (data: any) => void;
  'error': (error: Error) => void;
  'pixGenerated': (brCode: string) => void;
  'styleApplied': (style: any) => void;
}
