/**
 * QR Frame applier for adding decorative frames
 * Implements Single Responsibility Principle - only handles frame application
 */
export class QRFrameApplier {
  private frameStyles: Record<string, string>;

  constructor() {
    this.frameStyles = this.initializeFrameStyles();
  }

  /**
   * Applies frame to QR code
   * @param qrBuffer QR code buffer
   * @param frameStyle Frame style name
   * @returns Promise with frame applied
   */
  async applyFrame(qrBuffer: Buffer, frameStyle: string): Promise<Buffer> {
    try {
      const frameSvg = this.getFrameSvg(frameStyle);
      if (!frameSvg) {
        console.warn(`Frame style '${frameStyle}' not found`);
        return qrBuffer;
      }

      return await this.compositeWithFrame(qrBuffer, frameSvg);
    } catch (error) {
      console.error('Failed to apply frame:', error);
      return qrBuffer;
    }
  }

  /**
   * Gets frame SVG by style name
   * @param styleNam Frame style name
   * @returns SVG string or null if not found
   */
  private getFrameSvg(styleName: string): string | null {
    return this.frameStyles[styleName.toLowerCase()] || null;
  }

  /**
   * Composites QR code with frame
   * @param qrBuffer QR code buffer
   * @param frameSvg Frame SVG string
   * @returns Promise with composited result
   */
  private async compositeWithFrame(qrBuffer: Buffer, frameSvg: string): Promise<Buffer> {
    // This would:
    // 1. Convert frame SVG to buffer
    // 2. Resize QR code to fit in frame
    // 3. Calculate position to center QR code in frame
    // 4. Composite QR code over frame
    
    console.warn('Frame composition not implemented');
    return qrBuffer;
  }

  /**
   * Initializes frame styles
   * @returns Record of frame styles
   */
  private initializeFrameStyles(): Record<string, string> {
    return {
      'clean': `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="350" viewBox="0 0 300 350">
        <rect width="300" height="350" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2"/>
        <rect x="25" y="25" width="250" height="250" fill="white"/>
        <text x="150" y="315" text-anchor="middle" fill="#6c757d" font-family="Arial, sans-serif" font-size="14">Scan to Pay</text>
      </svg>`,

      'tech': `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="350" viewBox="0 0 300 350">
        <defs>
          <linearGradient id="techGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#1a1a1a;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#333333;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="300" height="350" fill="url(#techGrad)"/>
        <rect x="20" y="20" width="260" height="260" fill="#000000" stroke="#00ff00" stroke-width="2"/>
        <text x="150" y="320" text-anchor="middle" fill="#00ff00" font-family="Courier, monospace" font-size="12">SCAN QR CODE</text>
      </svg>`,

      'creative': `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="350" viewBox="0 0 300 350">
        <defs>
          <radialGradient id="creativeGrad" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:1" />
            <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:1" />
          </radialGradient>
        </defs>
        <rect width="300" height="350" fill="url(#creativeGrad)"/>
        <circle cx="150" cy="150" r="130" fill="white" stroke="#ffffff" stroke-width="4"/>
        <text x="150" y="320" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Scan Me!</text>
      </svg>`,

      'pay': `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="350" viewBox="0 0 300 350">
        <rect width="300" height="350" fill="#2c3e50"/>
        <rect x="15" y="15" width="270" height="270" fill="#ecf0f1" rx="15"/>
        <rect x="25" y="25" width="250" height="250" fill="white" rx="10"/>
        <text x="150" y="315" text-anchor="middle" fill="#ecf0f1" font-family="Arial, sans-serif" font-size="14" font-weight="bold">PIX Payment</text>
        <circle cx="50" cy="315" r="8" fill="#27ae60"/>
        <circle cx="250" cy="315" r="8" fill="#27ae60"/>
      </svg>`,

      'scan_me_purple': `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="350" viewBox="0 0 300 350">
        <defs>
          <linearGradient id="purpleGrad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#8e44ad;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#3498db;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="300" height="350" fill="url(#purpleGrad)"/>
        <rect x="20" y="20" width="260" height="260" fill="white" rx="20"/>
        <text x="150" y="320" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="18" font-weight="bold">SCAN ME</text>
      </svg>`,

      'scan_me_neon': `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="350" viewBox="0 0 300 350">
        <rect width="300" height="350" fill="#000000"/>
        <rect x="20" y="20" width="260" height="260" fill="#000000" stroke="#00ff00" stroke-width="3" rx="10"/>
        <rect x="25" y="25" width="250" height="250" fill="#001100" rx="5"/>
        <text x="150" y="320" text-anchor="middle" fill="#00ff00" font-family="Courier, monospace" font-size="16" font-weight="bold">SCAN ME</text>
        <circle cx="50" cy="320" r="3" fill="#00ff00"/>
        <circle cx="250" cy="320" r="3" fill="#00ff00"/>
      </svg>`,

      'scan_me_tech': `<svg xmlns="http://www.w3.org/2000/svg" width="300" height="350" viewBox="0 0 300 350">
        <defs>
          <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#333" stroke-width="0.5"/>
          </pattern>
        </defs>
        <rect width="300" height="350" fill="#1a1a1a"/>
        <rect width="300" height="350" fill="url(#grid)"/>
        <rect x="25" y="25" width="250" height="250" fill="#000000" stroke="#0099ff" stroke-width="2"/>
        <text x="150" y="320" text-anchor="middle" fill="#0099ff" font-family="Courier, monospace" font-size="14">SCAN QR CODE</text>
        <rect x="10" y="10" width="20" height="20" fill="#0099ff"/>
        <rect x="270" y="10" width="20" height="20" fill="#0099ff"/>
        <rect x="10" y="320" width="20" height="20" fill="#0099ff"/>
        <rect x="270" y="320" width="20" height="20" fill="#0099ff"/>
      </svg>`
    };
  }

  /**
   * Gets available frame styles
   * @returns Array of frame style names
   */
  getAvailableFrameStyles(): string[] {
    return Object.keys(this.frameStyles);
  }

  /**
   * Checks if frame style exists
   * @param styleName Frame style name
   * @returns true if exists
   */
  hasFrameStyle(styleName: string): boolean {
    return styleName.toLowerCase() in this.frameStyles;
  }

  /**
   * Adds custom frame style
   * @param name Frame style name
   * @param svgContent SVG content
   */
  addCustomFrameStyle(name: string, svgContent: string): void {
    this.frameStyles[name.toLowerCase()] = svgContent;
  }

  /**
   * Removes frame style
   * @param name Frame style name
   */
  removeFrameStyle(name: string): void {
    delete this.frameStyles[name.toLowerCase()];
  }

  /**
   * Validates frame SVG
   * @param svgContent SVG content
   * @returns Validation result
   */
  validateFrameSvg(svgContent: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!svgContent || typeof svgContent !== 'string') {
      errors.push('SVG content must be a non-empty string');
      return { isValid: false, errors };
    }

    if (!svgContent.includes('<svg')) {
      errors.push('Content must be valid SVG');
    }

    if (!svgContent.includes('width=') || !svgContent.includes('height=')) {
      errors.push('SVG must have width and height attributes');
    }

    // Check for reasonable dimensions
    const widthMatch = svgContent.match(/width="(\d+)"/);
    const heightMatch = svgContent.match(/height="(\d+)"/);
    
    if (widthMatch && heightMatch) {
      const width = parseInt(widthMatch[1]);
      const height = parseInt(heightMatch[1]);
      
      if (width < 200 || width > 500) {
        errors.push('SVG width should be between 200 and 500 pixels');
      }
      
      if (height < 200 || height > 500) {
        errors.push('SVG height should be between 200 and 500 pixels');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Gets frame preview
   * @param styleName Frame style name
   * @returns Frame preview as base64 data URL
   */
  async getFramePreview(styleName: string): Promise<string | null> {
    const frameSvg = this.getFrameSvg(styleName);
    if (!frameSvg) {
      return null;
    }

    // This would convert SVG to base64 data URL for preview
    const base64 = Buffer.from(frameSvg).toString('base64');
    return `data:image/svg+xml;base64,${base64}`;
  }

  /**
   * Calculates QR position in frame
   * @param frameWidth Frame width
   * @param frameHeight Frame height
   * @param qrSize QR code size
   * @returns Position coordinates
   */
  calculateQRPosition(frameWidth: number, frameHeight: number, qrSize: number): {
    x: number;
    y: number;
  } {
    // Standard positioning - QR code centered with some top offset for text
    return {
      x: Math.round((frameWidth - qrSize) / 2),
      y: 35 // Standard top offset
    };
  }
}
