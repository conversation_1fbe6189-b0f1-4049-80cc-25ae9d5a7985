import { IBRCodeParser } from "../../core/interfaces/IQRGenerator";
import { IPixData, ParsedBRCode, BRCodeFields, PixMerchantFields, PixAdditionalFields } from "../../types/pix";

/**
 * BR-Code parser implementation
 * Implements Single Responsibility Principle - only handles BR-Code parsing
 */
export class BRCodeParser implements IBRCodeParser {
  /**
   * Parses a BR-Code string into PIX data
   * @param brCode BR-Code string to parse
   * @returns Parsed PIX data
   */
  parse(brCode: string): IPixData {
    if (!this.isValidBRCode(brCode)) {
      throw new Error('Invalid BR-Code format');
    }

    const fields = this.extractAllFields(brCode);
    const merchantInfo = this.parseMerchantAccountInfo(fields[BRCodeFields.MERCHANT_ACCOUNT_INFORMATION]);
    const additionalData = this.parseAdditionalDataField(fields[BRCodeFields.ADDITIONAL_DATA_FIELD]);

    return {
      key: merchantInfo[PixMerchantFields.PIX_KEY] || '',
      nameReceiver: fields[BRCodeFields.MERCHANT_NAME] || '',
      cityReceiver: fields[BRCodeFields.MERCHANT_CITY] || '',
      amount: fields[BRCodeFields.TRANSACTION_AMOUNT] ? parseFloat(fields[BRCodeFields.TRANSACTION_AMOUNT]) : undefined,
      zipcodeReceiver: fields[BRCodeFields.POSTAL_CODE],
      identification: additionalData[PixAdditionalFields.TXID],
      description: merchantInfo[PixMerchantFields.DESCRIPTION] || additionalData[PixAdditionalFields.REFERENCE_LABEL],
      defaultUrlPix: merchantInfo[PixMerchantFields.URL],
      singleTransaction: fields[BRCodeFields.POINT_OF_INITIATION_METHOD] === '12'
    };
  }

  /**
   * Validates BR-Code format
   * @param brCode BR-Code string to validate
   * @returns true if valid
   */
  isValidBRCode(brCode: string): boolean {
    if (!brCode || typeof brCode !== 'string') {
      return false;
    }

    // Check minimum length
    if (brCode.length < 50) {
      return false;
    }

    // Check if it starts with payload format indicator
    if (!brCode.startsWith('0002')) {
      return false;
    }

    // Check if it contains CRC field
    if (!brCode.includes('6304')) {
      return false;
    }

    // Validate CRC
    const crcIndex = brCode.lastIndexOf('6304');
    if (crcIndex === -1) {
      return false;
    }

    const dataWithoutCrc = brCode.substring(0, crcIndex + 4);
    const providedCrc = brCode.substring(crcIndex + 4, crcIndex + 8);
    const calculatedCrc = this.calculateCRC16(dataWithoutCrc);

    return providedCrc === calculatedCrc;
  }

  /**
   * Extracts all fields from BR-Code
   * @param brCode BR-Code string
   * @returns Object with all fields
   */
  private extractAllFields(brCode: string): Record<string, string> {
    const fields: Record<string, string> = {};
    let position = 0;

    while (position < brCode.length - 4) { // -4 for CRC field
      if (position + 4 > brCode.length) break;

      const tag = brCode.substring(position, position + 2);
      const lengthStr = brCode.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);

      if (isNaN(length) || length < 0) break;
      if (position + 4 + length > brCode.length) break;

      const value = brCode.substring(position + 4, position + 4 + length);
      fields[tag] = value;

      position += 4 + length;
    }

    return fields;
  }

  /**
   * Parses merchant account information (field 26)
   * @param merchantInfo Merchant account info string
   * @returns Parsed merchant fields
   */
  private parseMerchantAccountInfo(merchantInfo: string | undefined): Record<string, string> {
    if (!merchantInfo) return {};

    const fields: Record<string, string> = {};
    let position = 0;

    while (position < merchantInfo.length) {
      if (position + 4 > merchantInfo.length) break;

      const tag = merchantInfo.substring(position, position + 2);
      const lengthStr = merchantInfo.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);

      if (isNaN(length) || length < 0) break;
      if (position + 4 + length > merchantInfo.length) break;

      const value = merchantInfo.substring(position + 4, position + 4 + length);
      fields[tag] = value;

      position += 4 + length;
    }

    return fields;
  }

  /**
   * Parses additional data field (field 62)
   * @param additionalData Additional data string
   * @returns Parsed additional fields
   */
  private parseAdditionalDataField(additionalData: string | undefined): Record<string, string> {
    if (!additionalData) return {};

    const fields: Record<string, string> = {};
    let position = 0;

    while (position < additionalData.length) {
      if (position + 4 > additionalData.length) break;

      const tag = additionalData.substring(position, position + 2);
      const lengthStr = additionalData.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);

      if (isNaN(length) || length < 0) break;
      if (position + 4 + length > additionalData.length) break;

      const value = additionalData.substring(position + 4, position + 4 + length);
      fields[tag] = value;

      position += 4 + length;
    }

    return fields;
  }

  /**
   * Calculates CRC16 checksum
   * @param data Data to calculate CRC for
   * @returns CRC16 checksum as hex string
   */
  private calculateCRC16(data: string): string {
    const polynomial = 0x1021;
    let crc = 0xFFFF;

    for (let i = 0; i < data.length; i++) {
      crc ^= (data.charCodeAt(i) << 8);

      for (let j = 0; j < 8; j++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ polynomial;
        } else {
          crc <<= 1;
        }
        crc &= 0xFFFF;
      }
    }

    return crc.toString(16).toUpperCase().padStart(4, '0');
  }

  /**
   * Gets specific field value from BR-Code
   * @param brCode BR-Code string
   * @param tag Field tag
   * @returns Field value or null if not found
   */
  getFieldValue(brCode: string, tag: string): string | null {
    const fields = this.extractAllFields(brCode);
    return fields[tag] || null;
  }

  /**
   * Gets PIX key from BR-Code
   * @param brCode BR-Code string
   * @returns PIX key or null if not found
   */
  getPixKey(brCode: string): string | null {
    const merchantInfo = this.getFieldValue(brCode, BRCodeFields.MERCHANT_ACCOUNT_INFORMATION);
    if (!merchantInfo) return null;

    const merchantFields = this.parseMerchantAccountInfo(merchantInfo);
    return merchantFields[PixMerchantFields.PIX_KEY] || null;
  }

  /**
   * Gets transaction amount from BR-Code
   * @param brCode BR-Code string
   * @returns Transaction amount or null if not specified
   */
  getAmount(brCode: string): number | null {
    const amountStr = this.getFieldValue(brCode, BRCodeFields.TRANSACTION_AMOUNT);
    return amountStr ? parseFloat(amountStr) : null;
  }

  /**
   * Checks if BR-Code is for dynamic PIX
   * @param brCode BR-Code string
   * @returns true if dynamic PIX
   */
  isDynamicPix(brCode: string): boolean {
    const pointOfInitiation = this.getFieldValue(brCode, BRCodeFields.POINT_OF_INITIATION_METHOD);
    return pointOfInitiation === '12';
  }

  /**
   * Gets BR-Code summary information
   * @param brCode BR-Code string
   * @returns Summary object
   */
  getSummary(brCode: string): {
    isValid: boolean;
    isDynamic: boolean;
    hasAmount: boolean;
    pixKey?: string;
    receiverName?: string;
    receiverCity?: string;
    amount?: number;
    description?: string;
  } {
    if (!this.isValidBRCode(brCode)) {
      return { isValid: false, isDynamic: false, hasAmount: false };
    }

    const pixData = this.parse(brCode);

    return {
      isValid: true,
      isDynamic: pixData.singleTransaction || false,
      hasAmount: pixData.amount !== undefined,
      pixKey: pixData.key,
      receiverName: pixData.nameReceiver,
      receiverCity: pixData.cityReceiver,
      amount: pixData.amount,
      description: pixData.description
    };
  }

  /**
   * Converts parsed data back to formatted object
   * @param brCode BR-Code string
   * @returns Formatted parsed data
   */
  toFormattedObject(brCode: string): ParsedBRCode {
    const pixData = this.parse(brCode);

    return {
      nome: pixData.nameReceiver,
      cidade: pixData.cityReceiver,
      valor: pixData.amount,
      chave: pixData.key,
      txid: pixData.identification,
      cep: pixData.zipcodeReceiver,
      descricao: pixData.description,
      urlPix: pixData.defaultUrlPix
    };
  }
}
