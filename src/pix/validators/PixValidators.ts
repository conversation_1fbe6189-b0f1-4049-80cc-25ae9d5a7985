import { IPixValidator } from "../../core/interfaces/IQRGenerator";
import { PixKeyType } from "../../types/pix";

/**
 * PIX validators implementation
 * Implements Single Responsibility Principle - only handles PIX validation
 */
export class PixValidators implements IPixValidator {
  /**
   * Validates Brazilian CPF (Cadastro de Pessoas Físicas)
   * Based on the existing JavaScript implementation
   * @param cpf CPF number as string
   * @returns true if CPF is valid
   */
  validateCPF(cpf: string): boolean {
    const cleanCpf = cpf.replace(/\D/g, "");

    if (cleanCpf.length !== 11) return false;
    if (/^(\d)\1{10}$/.test(cleanCpf)) return false;

    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cleanCpf.charAt(i)) * (10 - i);
    }
    let digit1 = ((sum * 10) % 11) % 10;

    if (digit1 !== parseInt(cleanCpf.charAt(9))) return false;

    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cleanCpf.charAt(i)) * (11 - i);
    }
    let digit2 = ((sum * 10) % 11) % 10;

    return digit2 === parseInt(cleanCpf.charAt(10));
  }

  /**
   * Validates Brazilian CNPJ (Cadastro Nacional da Pessoa Jurídica)
   * @param cnpj CNPJ number as string
   * @returns true if CNPJ is valid
   */
  validateCNPJ(cnpj: string): boolean {
    if (!cnpj || typeof cnpj !== 'string') {
      return false;
    }

    const cleanCnpj = cnpj.replace(/\D/g, '');
    
    if (cleanCnpj.length !== 14) {
      return false;
    }

    // Check if all digits are the same
    if (/^(\d)\1+$/.test(cleanCnpj)) {
      return false;
    }

    const digits = cleanCnpj.split('').map(Number);
    
    // Validate first check digit
    let sum = 0;
    let weight = 5;
    for (let i = 0; i < 12; i++) {
      sum += digits[i] * weight;
      weight = weight === 2 ? 9 : weight - 1;
    }
    let checkDigit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
    if (checkDigit !== digits[12]) {
      return false;
    }

    // Validate second check digit
    sum = 0;
    weight = 6;
    for (let i = 0; i < 13; i++) {
      sum += digits[i] * weight;
      weight = weight === 2 ? 9 : weight - 1;
    }
    checkDigit = sum % 11 < 2 ? 0 : 11 - (sum % 11);
    if (checkDigit !== digits[13]) {
      return false;
    }

    return true;
  }

  /**
   * Validates phone number
   * Based on the existing JavaScript implementation
   * @param phone Phone number as string
   * @returns true if phone is valid
   */
  validatePhone(phone: string): boolean {
    const digits = phone.replace(/\D/g, "");
    return digits.length === 10 || digits.length === 11;
  }

  /**
   * Validates email address
   * Based on the existing JavaScript implementation
   * @param email Email address as string
   * @returns true if email is valid
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validates random PIX key
   * Based on the existing JavaScript implementation
   * @param key Random key as string
   * @returns true if key is valid
   */
  validateRandomKey(key: string): boolean {
    return key.length >= 10;
  }

  /**
   * Determines PIX key type
   * @param key PIX key
   * @returns PIX key type
   */
  getKeyType(key: string): PixKeyType | null {
    if (!key) return null;

    if (this.validateCPF(key)) return PixKeyType.CPF;
    if (this.validateCNPJ(key)) return PixKeyType.CNPJ;
    if (this.validateEmail(key)) return PixKeyType.EMAIL;
    if (this.validatePhone(key)) return PixKeyType.PHONE;
    if (this.validateRandomKey(key)) return PixKeyType.RANDOM;

    return null;
  }

  /**
   * Validates PIX key based on its type
   * @param key PIX key
   * @param expectedType Expected key type (optional)
   * @returns Validation result
   */
  validatePixKey(key: string, expectedType?: PixKeyType): {
    isValid: boolean;
    keyType: PixKeyType | null;
    errors: string[];
  } {
    const errors: string[] = [];
    const keyType = this.getKeyType(key);

    if (!keyType) {
      errors.push('Invalid PIX key format');
      return { isValid: false, keyType: null, errors };
    }

    if (expectedType && keyType !== expectedType) {
      errors.push(`Expected ${expectedType} but got ${keyType}`);
      return { isValid: false, keyType, errors };
    }

    return { isValid: true, keyType, errors: [] };
  }

  /**
   * Validates PIX amount
   * @param amount Amount value
   * @returns Validation result
   */
  validateAmount(amount: number): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (typeof amount !== 'number' || isNaN(amount)) {
      errors.push('Amount must be a valid number');
    } else if (amount < 0) {
      errors.push('Amount cannot be negative');
    } else if (amount > 999999999.99) {
      errors.push('Amount exceeds maximum limit');
    } else if (amount.toFixed(2).length > 13) {
      errors.push('Amount format exceeds 13 characters');
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Validates receiver name
   * @param name Receiver name
   * @returns Validation result
   */
  validateReceiverName(name: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!name || typeof name !== 'string') {
      errors.push('Receiver name is required');
    } else if (name.length > 25) {
      errors.push('Receiver name cannot exceed 25 characters');
    } else if (name.trim().length === 0) {
      errors.push('Receiver name cannot be empty');
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Validates receiver city
   * @param city Receiver city
   * @returns Validation result
   */
  validateReceiverCity(city: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!city || typeof city !== 'string') {
      errors.push('Receiver city is required');
    } else if (city.length > 15) {
      errors.push('Receiver city cannot exceed 15 characters');
    } else if (city.trim().length === 0) {
      errors.push('Receiver city cannot be empty');
    }

    return { isValid: errors.length === 0, errors };
  }
}
