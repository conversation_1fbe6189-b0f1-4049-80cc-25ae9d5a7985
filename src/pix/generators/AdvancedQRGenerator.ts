import { IQRGenerator, IQRGenerationOptions } from "../../core/interfaces/IQRGenerator";
import { QRCode } from "../../types";
import { StandardQRGenerator } from "./StandardQRGenerator";
import { QRStyleProcessor } from "../processors/QRStyleProcessor";
import { QRImageComposer } from "../composers/QRImageComposer";
import { QRFrameApplier } from "../processors/QRFrameApplier";

/**
 * Advanced QR generator with styling capabilities
 * Implements Single Responsibility Principle through composition
 */
export class AdvancedQRGenerator implements IQRGenerator {
  private standardGenerator: StandardQRGenerator;
  private styleProcessor: QRStyleProcessor;
  private imageComposer: QRImageComposer;
  private frameApplier: QRFrameApplier;

  constructor() {
    this.standardGenerator = new StandardQRGenerator();
    this.styleProcessor = new QRStyleProcessor();
    this.imageComposer = new QRImageComposer();
    this.frameApplier = new QRFrameApplier();
  }

  /**
   * Generates advanced styled QR Code
   * @param data Data to encode
   * @param options Generation options with styling
   * @returns QR Code instance
   */
  generate(data: string, options: IAdvancedQROptions): QRCode {
    // Generate base QR code
    const baseQR = this.standardGenerator.generate(data, options);

    // Apply advanced styling if specified
    if (options.styling) {
      return this.applyAdvancedStyling(baseQR, options.styling);
    }

    return baseQR;
  }

  /**
   * Validates input data
   * @param data Data to validate
   * @returns true if valid
   */
  validateData(data: string): boolean {
    return this.standardGenerator.validateData(data);
  }

  /**
   * Gets optimal mode for data
   * @param data Input data
   * @returns Optimal mode
   */
  getOptimalMode(data: string): string {
    return this.standardGenerator.getOptimalMode(data);
  }

  /**
   * Generates styled QR code as buffer
   * @param data Data to encode
   * @param options Advanced generation options
   * @returns Promise with QR code buffer
   */
  async generateBuffer(data: string, options: IAdvancedQROptions): Promise<Buffer> {
    // Generate base QR code buffer
    let qrBuffer = await this.generateBaseBuffer(data, options);

    // Apply styling in sequence
    if (options.styling) {
      qrBuffer = await this.applyBufferStyling(qrBuffer, options.styling);
    }

    return qrBuffer;
  }

  /**
   * Applies advanced styling to QR Code instance
   * @param qr Base QR Code
   * @param styling Styling options
   * @returns Styled QR Code
   */
  private applyAdvancedStyling(qr: QRCode, styling: IAdvancedStyling): QRCode {
    // This would apply styling to the QR Code instance
    // For now, return the base QR code
    console.warn('Advanced QR Code instance styling not implemented');
    return qr;
  }

  /**
   * Generates base QR code as buffer
   * @param data Data to encode
   * @param options Generation options
   * @returns Promise with base QR buffer
   */
  private async generateBaseBuffer(data: string, options: IAdvancedQROptions): Promise<Buffer> {
    // This would use a QR code library to generate buffer
    // Placeholder implementation
    const QRCode = require('qrcode');
    
    const qrOptions = {
      type: 'png' as const,
      margin: options.border || 4,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: (options.size || 10) * 25,
      errorCorrectionLevel: options.errorCorrectionLevel || 'M'
    };

    return await QRCode.toBuffer(data, qrOptions);
  }

  /**
   * Applies styling to QR code buffer
   * @param qrBuffer Base QR buffer
   * @param styling Styling options
   * @returns Promise with styled QR buffer
   */
  private async applyBufferStyling(qrBuffer: Buffer, styling: IAdvancedStyling): Promise<Buffer> {
    let styledBuffer = qrBuffer;

    // Apply gradient if specified
    if (styling.gradientColor && styling.gradientMode) {
      styledBuffer = await this.styleProcessor.applyGradient(
        styledBuffer, 
        styling.gradientColor, 
        styling.gradientMode
      );
    }

    // Apply custom markers
    if (styling.markerStyle || styling.borderStyle) {
      styledBuffer = await this.styleProcessor.applyCustomMarkers(
        styledBuffer, 
        styling.markerStyle, 
        styling.borderStyle
      );
    }

    // Add center image
    if (styling.centerImage) {
      styledBuffer = await this.imageComposer.addCenterImage(
        styledBuffer, 
        styling.centerImage, 
        styling.centerImageSize || 0.25
      );
    }

    // Apply frame
    if (styling.frameStyle) {
      styledBuffer = await this.frameApplier.applyFrame(styledBuffer, styling.frameStyle);
    }

    return styledBuffer;
  }

  /**
   * Validates advanced options
   * @param options Options to validate
   * @returns Validation result
   */
  validateAdvancedOptions(options: IAdvancedQROptions): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate base options
    const baseValidation = this.standardGenerator.validateParameters(options.data, options);
    errors.push(...baseValidation.errors);
    warnings.push(...baseValidation.warnings);

    // Validate styling options
    if (options.styling) {
      const stylingValidation = this.validateStylingOptions(options.styling);
      errors.push(...stylingValidation.errors);
      warnings.push(...stylingValidation.warnings);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validates styling options
   * @param styling Styling options
   * @returns Validation result
   */
  private validateStylingOptions(styling: IAdvancedStyling): {
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (styling.centerImageSize && (styling.centerImageSize <= 0 || styling.centerImageSize > 0.5)) {
      errors.push('Center image size must be between 0 and 0.5');
    }

    if (styling.gradientColor && !this.isValidColor(styling.gradientColor)) {
      errors.push('Invalid gradient color format');
    }

    if (styling.centerImage && styling.centerImageSize && styling.centerImageSize > 0.3) {
      warnings.push('Large center images may affect QR code readability');
    }

    return { errors, warnings };
  }

  /**
   * Checks if color is valid
   * @param color Color to validate
   * @returns true if valid
   */
  private isValidColor(color: string): boolean {
    return /^#[0-9A-Fa-f]{6}$/.test(color) || /^#[0-9A-Fa-f]{3}$/.test(color);
  }
}

/**
 * Advanced QR generation options
 */
export interface IAdvancedQROptions extends IQRGenerationOptions {
  data: string;
  size?: number;
  border?: number;
  styling?: IAdvancedStyling;
}

/**
 * Advanced styling options
 */
export interface IAdvancedStyling {
  markerStyle?: string;
  borderStyle?: string;
  lineStyle?: string;
  gradientColor?: string;
  gradientMode?: string;
  frameStyle?: string;
  styleMode?: 'Normal' | 'Full';
  centerImage?: string;
  centerImageSize?: number;
}
