import { IQRGenerator, IQRGenerationOptions } from "../../core/interfaces/IQRGenerator";
import { QRCode } from "../../types";
import qrcode from "qrcode-generator";

/**
 * Standard QR Code generator implementation
 * Implements Single Responsibility Principle - only handles standard QR generation
 */
export class StandardQRGenerator implements IQRGenerator {
  /**
   * Generates standard QR Code
   * @param data Data to encode
   * @param options Generation options
   * @returns QR Code instance
   */
  generate(data: string, options: IQRGenerationOptions): QRCode {
    if (!this.validateData(data)) {
      throw new Error('Invalid data for QR code generation');
    }

    const typeNumber = options.typeNumber || this.getOptimalTypeNumber(data);
    const errorCorrectionLevel = options.errorCorrectionLevel || 'M';
    const mode = options.mode || this.getOptimalMode(data);

    const qr = qrcode(typeNumber as any, errorCorrectionLevel);
    qr.addData(data, mode as any);
    qr.make();

    return qr;
  }

  /**
   * Validates input data
   * @param data Data to validate
   * @returns true if valid
   */
  validateData(data: string): boolean {
    if (!data || typeof data !== 'string') {
      return false;
    }

    // Basic validation - check length limits
    return data.length > 0 && data.length <= 4296; // QR Code maximum capacity
  }

  /**
   * Gets optimal mode for data
   * @param data Input data
   * @returns Optimal mode
   */
  getOptimalMode(data: string): string {
    // Numeric mode: only digits
    if (/^\d+$/.test(data)) {
      return 'Numeric';
    }

    // Alphanumeric mode: digits, uppercase letters, and specific symbols
    if (/^[0-9A-Z $%*+\-./:]+$/.test(data)) {
      return 'Alphanumeric';
    }

    // Kanji mode: Japanese characters (simplified check)
    if (/[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(data)) {
      return 'Kanji';
    }

    // Default to Byte mode
    return 'Byte';
  }

  /**
   * Gets optimal type number based on data length and mode
   * @param data Input data
   * @returns Optimal type number
   */
  private getOptimalTypeNumber(data: string): number {
    const mode = this.getOptimalMode(data);
    const dataLength = data.length;

    // Simplified capacity calculation
    // In practice, this would use proper capacity tables
    const capacityEstimates = {
      'Numeric': {
        1: 41, 2: 77, 3: 127, 4: 187, 5: 255, 6: 322, 7: 370, 8: 461, 9: 552, 10: 652
      },
      'Alphanumeric': {
        1: 25, 2: 47, 3: 77, 4: 114, 5: 154, 6: 195, 7: 224, 8: 279, 9: 335, 10: 395
      },
      'Byte': {
        1: 17, 2: 32, 3: 53, 4: 78, 5: 106, 6: 134, 7: 154, 8: 192, 9: 230, 10: 271
      },
      'Kanji': {
        1: 10, 2: 20, 3: 32, 4: 48, 5: 65, 6: 82, 7: 95, 8: 118, 9: 141, 10: 167
      }
    };

    const capacities = capacityEstimates[mode as keyof typeof capacityEstimates] || capacityEstimates.Byte;

    for (let typeNumber = 1; typeNumber <= 10; typeNumber++) {
      if (dataLength <= capacities[typeNumber as keyof typeof capacities]) {
        return typeNumber;
      }
    }

    // For larger data, estimate based on growth pattern
    return Math.min(40, Math.ceil(dataLength / 20));
  }

  /**
   * Gets data capacity for given parameters
   * @param typeNumber QR Code type number
   * @param errorCorrectionLevel Error correction level
   * @param mode Data mode
   * @returns Data capacity
   */
  getDataCapacity(typeNumber: number, errorCorrectionLevel: string, mode: string): number {
    // Simplified capacity calculation
    // In practice, this would use official QR Code capacity tables
    const baseCapacity = typeNumber * 20;
    const correctionMultiplier = {
      'L': 1.0,
      'M': 0.8,
      'Q': 0.65,
      'H': 0.5
    }[errorCorrectionLevel] || 0.8;

    const modeMultiplier = {
      'Numeric': 1.2,
      'Alphanumeric': 1.0,
      'Byte': 0.8,
      'Kanji': 0.5
    }[mode] || 0.8;

    return Math.floor(baseCapacity * correctionMultiplier * modeMultiplier);
  }

  /**
   * Validates QR Code parameters
   * @param data Input data
   * @param options Generation options
   * @returns Validation result
   */
  validateParameters(data: string, options: IQRGenerationOptions): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate data
    if (!this.validateData(data)) {
      errors.push('Invalid input data');
    }

    // Validate type number
    if (options.typeNumber && (options.typeNumber < 1 || options.typeNumber > 40)) {
      errors.push('Type number must be between 1 and 40');
    }

    // Validate error correction level
    if (options.errorCorrectionLevel && !['L', 'M', 'Q', 'H'].includes(options.errorCorrectionLevel)) {
      errors.push('Error correction level must be L, M, Q, or H');
    }

    // Validate mode
    if (options.mode && !['Numeric', 'Alphanumeric', 'Byte', 'Kanji'].includes(options.mode)) {
      errors.push('Mode must be Numeric, Alphanumeric, Byte, or Kanji');
    }

    // Check capacity
    if (options.typeNumber && options.errorCorrectionLevel && options.mode) {
      const capacity = this.getDataCapacity(options.typeNumber, options.errorCorrectionLevel, options.mode);
      if (data.length > capacity) {
        errors.push(`Data length (${data.length}) exceeds capacity (${capacity}) for given parameters`);
      }
    }

    // Performance warnings
    if (data.length > 1000) {
      warnings.push('Large data size may affect QR code readability');
    }

    if (options.typeNumber && options.typeNumber > 20) {
      warnings.push('High type numbers may be difficult to scan');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Gets recommended parameters for data
   * @param data Input data
   * @returns Recommended parameters
   */
  getRecommendedParameters(data: string): {
    typeNumber: number;
    errorCorrectionLevel: string;
    mode: string;
  } {
    const mode = this.getOptimalMode(data);
    const typeNumber = this.getOptimalTypeNumber(data);
    
    // Choose error correction level based on use case
    let errorCorrectionLevel = 'M'; // Default
    
    if (data.length < 100) {
      errorCorrectionLevel = 'H'; // High correction for small data
    } else if (data.length > 1000) {
      errorCorrectionLevel = 'L'; // Low correction for large data
    }

    return {
      typeNumber,
      errorCorrectionLevel,
      mode
    };
  }
}
