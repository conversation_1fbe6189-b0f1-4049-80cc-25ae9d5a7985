import { IPixData } from "../../types/pix";

/**
 * BR-Code generator for PIX transactions
 * Implements Single Responsibility Principle - only handles BR-Code generation
 */
export class BRCodeGenerator {
  private static readonly GUI = "br.gov.bcb.pix";

  /**
   * Generates BR-Code for PIX transaction
   * Based on the existing JavaScript implementation
   * @param pixData PIX transaction data
   * @returns BR-Code string
   */
  generateBRCode(pixData: IPixData): string {
    // Build account information (field 26)
    const basePix = this.formatTLV("00", BRCodeGenerator.GUI);
    let infoString = this.formatTLV("01", pixData.key);

    if (pixData.description) {
      infoString += this.formatTLV("02", this.formatText(pixData.description));
    }

    const accountInfo = this.formatTLV("26", basePix + infoString);

    // Build additional data field (field 62)
    const txid = pixData.identification || "***";
    const additionalData = this.formatTLV("62", this.formatTLV("05", this.formatText(txid)));

    // Build complete BR Code
    let resultString = this.formatTLV("00", "01"); // Payload Format Indicator
    resultString += this.formatTLV("01", "11"); // Point of Initiation Method (static)
    resultString += accountInfo;
    resultString += this.formatTLV("52", "0000"); // Merchant Category Code
    resultString += this.formatTLV("53", "986"); // Transaction Currency (BRL)

    if (pixData.amount && pixData.amount > 0) {
      resultString += this.formatTLV("54", pixData.amount.toFixed(2));
    }

    resultString += this.formatTLV("58", "BR"); // Country Code
    resultString += this.formatTLV("59", this.formatText(pixData.nameReceiver)); // Merchant Name
    resultString += this.formatTLV("60", this.formatText(pixData.cityReceiver)); // Merchant City
    resultString += additionalData;
    resultString += "6304"; // CRC placeholder

    const finalBRCode = resultString + this.calculateCRC16(resultString);

    return finalBRCode;
  }



  /**
   * Formats a TLV (Tag-Length-Value) field
   * @param tag Field tag
   * @param value Field value
   * @returns Formatted TLV string
   */
  private formatTLV(tag: string, value: string): string {
    const length = value.length.toString().padStart(2, '0');
    return tag + length + value;
  }

  /**
   * Formats text for PIX standards (removes accents, keeps original case)
   * Based on the existing JavaScript implementation
   * @param text Text to format
   * @returns Formatted text
   */
  private formatText(text: string): string {
    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove accents
      .replace(/[^A-Za-z0-9$@%*+\-./:_ ]/g, '') // Keep PIX allowed characters
      .trim();
  }

  /**
   * Calculates CRC16 checksum for BR-Code
   * Based on the existing JavaScript implementation
   * @param data Data to calculate CRC for
   * @returns CRC16 checksum as hex string
   */
  private calculateCRC16(data: string): string {
    // Simplified CRC16 calculation matching JavaScript implementation
    let crc = 0xffff;
    const dataBytes = new TextEncoder().encode(data);

    for (const byte of dataBytes) {
      crc ^= byte << 8;
      for (let i = 0; i < 8; i++) {
        if (crc & 0x8000) {
          crc = (crc << 1) ^ 0x1021;
        } else {
          crc <<= 1;
        }
      }
    }

    return (crc & 0xffff).toString(16).toUpperCase().padStart(4, "0");
  }

  /**
   * Validates BR-Code format
   * @param brCode BR-Code to validate
   * @returns Validation result
   */
  validateBRCode(brCode: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!brCode || typeof brCode !== 'string') {
      errors.push('BR-Code must be a string');
      return { isValid: false, errors };
    }

    // Check minimum length
    if (brCode.length < 50) {
      errors.push('BR-Code is too short');
    }

    // Check if it ends with CRC
    if (!brCode.includes('6304')) {
      errors.push('BR-Code must contain CRC field');
    }

    // Validate CRC
    const crcIndex = brCode.lastIndexOf('6304');
    if (crcIndex !== -1) {
      const dataWithoutCrc = brCode.substring(0, crcIndex + 4);
      const providedCrc = brCode.substring(crcIndex + 4, crcIndex + 8);
      const calculatedCrc = this.calculateCRC16(dataWithoutCrc);
      
      if (providedCrc !== calculatedCrc) {
        errors.push('Invalid CRC checksum');
      }
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Gets BR-Code field value by tag
   * @param brCode BR-Code string
   * @param tag Field tag
   * @returns Field value or null if not found
   */
  getFieldValue(brCode: string, tag: string): string | null {
    const regex = new RegExp(tag + '(\\d{2})(.{0,99})');
    const match = brCode.match(regex);
    
    if (match) {
      const length = parseInt(match[1], 10);
      return match[2].substring(0, length);
    }
    
    return null;
  }

  /**
   * Extracts all fields from BR-Code
   * @param brCode BR-Code string
   * @returns Object with all extracted fields
   */
  extractFields(brCode: string): Record<string, string> {
    const fields: Record<string, string> = {};
    let position = 0;

    while (position < brCode.length - 4) { // -4 for CRC field
      const tag = brCode.substring(position, position + 2);
      const lengthStr = brCode.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);
      
      if (isNaN(length)) break;
      
      const value = brCode.substring(position + 4, position + 4 + length);
      fields[tag] = value;
      
      position += 4 + length;
    }

    return fields;
  }
}
