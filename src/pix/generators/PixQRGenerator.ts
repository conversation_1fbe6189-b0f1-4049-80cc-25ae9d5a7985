import { IPixGenerator, IQRGenerationOptions, IPixData, PixKeyType } from "../../core/interfaces/IQRGenerator";
import { QRCode } from "../../types";
import { BRCodeGenerator } from "./BRCodeGenerator";
import { PixValidators } from "../validators/PixValidators";
import qrcode from "qrcode-generator";

/**
 * PIX QR Code generator implementation
 * Implements Single Responsibility Principle - only handles PIX QR generation
 */
export class PixQRGenerator implements IPixGenerator {
  private brCodeGenerator: BRCodeGenerator;
  private validators: PixValidators;

  constructor() {
    this.brCodeGenerator = new BRCodeGenerator();
    this.validators = new PixValidators();
  }

  /**
   * Generates QR Code for PIX transaction
   * @param data PIX data or BR-Code string
   * @param options Generation options
   * @returns QR Code instance
   */
  generate(data: string, options: IQRGenerationOptions): QRCode {
    let brCode: string;

    // Check if data is already a BR-Code or needs to be generated
    if (this.isBRCode(data)) {
      brCode = data;
    } else {
      // Assume data is JSON string with PIX data
      try {
        const pixData = JSON.parse(data) as IPixData;
        brCode = this.generateBRCode(pixData);
      } catch {
        throw new Error('Invalid PIX data format');
      }
    }

    // Validate BR-Code
    const validation = this.brCodeGenerator.validateBRCode(brCode);
    if (!validation.isValid) {
      throw new Error(`Invalid BR-Code: ${validation.errors.join(', ')}`);
    }

    // Generate QR Code
    const qr = qrcode((options.typeNumber || 0) as any, options.errorCorrectionLevel || 'M');
    qr.addData(brCode, (options.mode || 'Byte') as any);
    qr.make();

    return qr;
  }

  /**
   * Generates BR-Code for PIX transaction
   * @param pixData PIX transaction data
   * @returns BR-Code string
   */
  generateBRCode(pixData: IPixData): string {
    // Validate PIX data
    this.validatePixData(pixData);
    
    return this.brCodeGenerator.generateBRCode(pixData);
  }

  /**
   * Validates PIX data
   * @param pixData PIX data to validate
   * @throws Error if validation fails
   */
  private validatePixData(pixData: IPixData): void {
    const errors: string[] = [];

    // Validate required fields
    if (!pixData.key) {
      errors.push('PIX key is required');
    } else {
      const keyValidation = this.validators.validatePixKey(pixData.key);
      if (!keyValidation.isValid) {
        errors.push(...keyValidation.errors);
      }
    }

    if (!pixData.nameReceiver) {
      errors.push('Receiver name is required');
    } else {
      const nameValidation = this.validators.validateReceiverName(pixData.nameReceiver);
      if (!nameValidation.isValid) {
        errors.push(...nameValidation.errors);
      }
    }

    if (!pixData.cityReceiver) {
      errors.push('Receiver city is required');
    } else {
      const cityValidation = this.validators.validateReceiverCity(pixData.cityReceiver);
      if (!cityValidation.isValid) {
        errors.push(...cityValidation.errors);
      }
    }

    // Validate optional fields
    if (pixData.amount !== undefined) {
      const amountValidation = this.validators.validateAmount(pixData.amount);
      if (!amountValidation.isValid) {
        errors.push(...amountValidation.errors);
      }
    }

    if (errors.length > 0) {
      throw new Error(`PIX validation failed: ${errors.join(', ')}`);
    }
  }

  /**
   * Validates input data
   * @param data Data to validate
   * @returns true if valid
   */
  validateData(data: string): boolean {
    if (!data || typeof data !== 'string') {
      return false;
    }

    // Check if it's a BR-Code
    if (this.isBRCode(data)) {
      const validation = this.brCodeGenerator.validateBRCode(data);
      return validation.isValid;
    }

    // Check if it's valid JSON with PIX data
    try {
      const pixData = JSON.parse(data) as IPixData;
      this.validatePixData(pixData);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Gets optimal mode for PIX data (always Byte for BR-Code)
   * @param data Input data
   * @returns Optimal mode
   */
  getOptimalMode(data: string): string {
    return 'Byte'; // BR-Code always uses Byte mode
  }

  /**
   * Validates PIX key
   * @param key PIX key
   * @param type Expected key type
   * @returns true if valid
   */
  validatePixKey(key: string, type: PixKeyType): boolean {
    const validation = this.validators.validatePixKey(key, type);
    return validation.isValid;
  }

  /**
   * Calculates CRC for BR-Code
   * @param data Data for CRC calculation
   * @returns CRC string
   */
  calculateCRC(data: string): string {
    return this.brCodeGenerator['calculateCRC16'](data);
  }

  /**
   * Checks if string is a BR-Code
   * @param data String to check
   * @returns true if it's a BR-Code
   */
  private isBRCode(data: string): boolean {
    // BR-Code starts with "00020101" (payload format indicator + point of initiation)
    return data.startsWith('0002') && data.includes('6304') && data.length > 50;
  }

  /**
   * Parses BR-Code to PIX data
   * @param brCode BR-Code string
   * @returns Parsed PIX data
   */
  parseBRCode(brCode: string): IPixData {
    const validation = this.brCodeGenerator.validateBRCode(brCode);
    if (!validation.isValid) {
      throw new Error(`Invalid BR-Code: ${validation.errors.join(', ')}`);
    }

    const fields = this.brCodeGenerator.extractFields(brCode);
    
    // Extract merchant account info (field 26)
    const merchantInfo = fields['26'];
    const merchantFields = this.extractMerchantFields(merchantInfo);

    return {
      key: merchantFields['01'] || '',
      nameReceiver: fields['59'] || '',
      cityReceiver: fields['60'] || '',
      amount: fields['54'] ? parseFloat(fields['54']) : undefined,
      zipcodeReceiver: fields['61'],
      identification: this.extractAdditionalField(fields['62'], '05'),
      description: merchantFields['02'] || this.extractAdditionalField(fields['62'], '25'),
      defaultUrlPix: merchantFields['25'],
      singleTransaction: fields['01'] === '12'
    };
  }

  /**
   * Extracts merchant account fields
   * @param merchantInfo Merchant info string
   * @returns Extracted fields
   */
  private extractMerchantFields(merchantInfo: string): Record<string, string> {
    const fields: Record<string, string> = {};
    let position = 0;

    while (position < merchantInfo.length) {
      const tag = merchantInfo.substring(position, position + 2);
      const lengthStr = merchantInfo.substring(position + 2, position + 4);
      const length = parseInt(lengthStr, 10);
      
      if (isNaN(length)) break;
      
      const value = merchantInfo.substring(position + 4, position + 4 + length);
      fields[tag] = value;
      
      position += 4 + length;
    }

    return fields;
  }

  /**
   * Extracts field from additional data
   * @param additionalData Additional data string
   * @param tag Field tag
   * @returns Field value or undefined
   */
  private extractAdditionalField(additionalData: string | undefined, tag: string): string | undefined {
    if (!additionalData) return undefined;

    const regex = new RegExp(tag + '(\\d{2})(.*)');
    const match = additionalData.match(regex);
    
    if (match) {
      const length = parseInt(match[1], 10);
      return match[2].substring(0, length);
    }
    
    return undefined;
  }

  /**
   * Gets supported PIX key types
   * @returns Array of supported key types
   */
  getSupportedKeyTypes(): PixKeyType[] {
    return [PixKeyType.CPF, PixKeyType.CNPJ, PixKeyType.EMAIL, PixKeyType.PHONE, PixKeyType.RANDOM];
  }

  /**
   * Gets PIX transaction limits
   * @returns Transaction limits
   */
  getTransactionLimits(): {
    minAmount: number;
    maxAmount: number;
    maxNameLength: number;
    maxCityLength: number;
    maxDescriptionLength: number;
  } {
    return {
      minAmount: 0.01,
      maxAmount: 999999999.99,
      maxNameLength: 25,
      maxCityLength: 15,
      maxDescriptionLength: 72
    };
  }
}
