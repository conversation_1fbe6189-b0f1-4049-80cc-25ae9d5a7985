import { PixQRApp } from "./PixQRApp";

/**
 * Main entry point for the PIX QR Code application
 * Initializes the application when the DOM is ready
 */

// Global functions for preset buttons (called from HTML)
declare global {
  interface Window {
    applyPreset: (presetName: string) => void;
  }
}

// Initialize application when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  new PixQRApp();

  // Make preset function globally available
  window.applyPreset = (presetName: string) => {
    applyPreset(presetName);
  };
});

/**
 * Apply preset configurations to the QR code customization
 * @param presetName Name of the preset to apply
 */
function applyPreset(presetName: string): void {
  const presets: Record<string, Record<string, string | boolean>> = {
    modern: {
      "dots-type": "rounded",
      "dots-color": "#2563eb",
      "corner-square-type": "extra-rounded",
      "corner-square-color": "#1d4ed8",
      "corner-dot-type": "dot",
      "corner-dot-color": "#1e40af",
      "background-color": "#ffffff"
    },
    classic: {
      "dots-type": "square",
      "dots-color": "#000000",
      "corner-square-type": "square",
      "corner-square-color": "#000000",
      "corner-dot-type": "square",
      "corner-dot-color": "#000000",
      "background-color": "#ffffff"
    },
    elegant: {
      "dots-type": "classy-rounded",
      "dots-color": "#374151",
      "corner-square-type": "classy-rounded",
      "corner-square-color": "#1f2937",
      "corner-dot-type": "classy",
      "corner-dot-color": "#111827",
      "background-color": "#f9fafb"
    },
    vibrant: {
      "dots-type": "dots",
      "dots-color": "#dc2626",
      "corner-square-type": "extra-rounded",
      "corner-square-color": "#b91c1c",
      "corner-dot-type": "dot",
      "corner-dot-color": "#991b1b",
      "background-color": "#fef2f2"
    },
    circular: {
      "dots-type": "dots",
      "dots-color": "#7c3aed",
      "corner-square-type": "dot",
      "corner-square-color": "#6d28d9",
      "corner-dot-type": "dot",
      "corner-dot-color": "#5b21b6",
      "background-color": "#faf5ff"
    }
  };

  const preset = presets[presetName];
  if (!preset) {
    console.warn(`Preset '${presetName}' not found`);
    return;
  }

  // Apply preset values to form elements
  Object.entries(preset).forEach(([elementId, value]) => {
    const element = document.getElementById(elementId) as HTMLInputElement | HTMLSelectElement;
    if (element) {
      if (typeof value === "boolean") {
        (element as HTMLInputElement).checked = value;
      } else {
        element.value = value;
      }
      
      // Trigger change event to update the QR code
      element.dispatchEvent(new Event("change", { bubbles: true }));
    }
  });

  // Show success message
  showPresetToast(`Preset "${presetName}" aplicado com sucesso!`);
}

/**
 * Show a toast message for preset application
 * @param message Message to display
 */
function showPresetToast(message: string): void {
  const toast = document.createElement("div");
  toast.textContent = message;
  
  Object.assign(toast.style, {
    position: "fixed",
    top: "20px",
    left: "50%",
    transform: "translateX(-50%)",
    padding: "12px 24px",
    borderRadius: "8px",
    color: "white",
    backgroundColor: "#10b981",
    zIndex: "10000",
    fontSize: "14px",
    fontWeight: "500",
    boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
    opacity: "0",
    transition: "opacity 0.3s ease"
  });

  document.body.appendChild(toast);

  // Animate in
  setTimeout(() => {
    toast.style.opacity = "1";
  }, 100);

  // Remove after 2 seconds
  setTimeout(() => {
    toast.style.opacity = "0";
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, 2000);
}
