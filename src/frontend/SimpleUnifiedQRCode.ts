import QRCodeStyling from "../core/QRCodeStyling";
import { BRCodeGenerator } from "../pix/generators/BRCodeGenerator";
import { PixValidators } from "../pix/validators/PixValidators";
import { IPixData } from "../types/pix";
import { Options } from "../types";

/**
 * Simplified unified QR code class for frontend use
 * Combines basic QR code styling with PIX functionality
 */
export class SimpleUnifiedQRCode {
  private qrCodeStyling: QRCodeStyling;
  private brCodeGenerator: BRCodeGenerator;
  private pixValidators: PixValidators;

  constructor() {
    this.qrCodeStyling = new QRCodeStyling();
    this.brCodeGenerator = new BRCodeGenerator();
    this.pixValidators = new PixValidators();
  }

  /**
   * Updates QR code with new options
   * @param options QR code options
   */
  update(options: Partial<Options>): void {
    this.qrCodeStyling.update(options);
  }

  /**
   * Appends QR code to container
   * @param container Container element
   */
  append(container: HTMLElement): void {
    this.qrCodeStyling.append(container);
  }

  /**
   * Downloads QR code file
   * @param downloadOptions Download options
   */
  async download(downloadOptions: { name: string; extension: string }): Promise<void> {
    const extension = downloadOptions.extension as "png" | "jpeg" | "webp" | "svg";
    return this.qrCodeStyling.download(extension);
  }

  /**
   * Generates PIX BR-Code
   * @param pixData PIX transaction data
   * @returns BR-Code string
   */
  generatePix(pixData: IPixData): string {
    return this.brCodeGenerator.generateBRCode(pixData);
  }

  /**
   * Validates PIX key
   * @param key PIX key to validate
   * @returns true if valid
   */
  validatePixKey(key: string): boolean {
    const validation = this.pixValidators.validatePixKey(key);
    return validation.isValid;
  }

  /**
   * Validates CPF
   * @param cpf CPF to validate
   * @returns true if valid
   */
  validateCPF(cpf: string): boolean {
    return this.pixValidators.validateCPF(cpf);
  }

  /**
   * Validates phone
   * @param phone Phone to validate
   * @returns true if valid
   */
  validatePhone(phone: string): boolean {
    return this.pixValidators.validatePhone(phone);
  }

  /**
   * Validates email
   * @param email Email to validate
   * @returns true if valid
   */
  validateEmail(email: string): boolean {
    return this.pixValidators.validateEmail(email);
  }

  /**
   * Validates random key
   * @param key Random key to validate
   * @returns true if valid
   */
  validateRandomKey(key: string): boolean {
    return this.pixValidators.validateRandomKey(key);
  }
}
