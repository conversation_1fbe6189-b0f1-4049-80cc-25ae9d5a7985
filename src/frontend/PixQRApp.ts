import { SimpleUnifiedQRCode } from "./SimpleUnifiedQRCode";
import { IPixData } from "../types/pix";
import { Options } from "../types";

/**
 * Frontend application class that connects HTML interface with TypeScript API
 * Implements the existing JavaScript functionality using the refactored architecture
 */
export class PixQRApp {
  private unifiedQR: SimpleUnifiedQRCode;
  private currentBRCode: string = "";
  private currentImageFile: File | null = null;
  
  // DOM elements
  private form!: HTMLFormElement;
  private keyTypeSelect!: HTMLSelectElement;
  private pixKeyInput!: HTMLInputElement;
  private receiverNameInput!: HTMLInputElement;
  private receiverCityInput!: HTMLInputElement;
  private amountInput!: HTMLInputElement;
  private referenceInput!: HTMLInputElement;
  private descriptionInput!: HTMLInputElement;
  private generateBtn!: HTMLButtonElement;
  private loadingSpinner!: HTMLElement;
  private qrPlaceholder!: HTMLElement;
  private qrResult!: HTMLElement;
  private qrPreview!: HTMLElement;
  private brCodeText!: HTMLElement;
  private pixDetails!: HTMLElement;
  private downloadBtn!: HTMLButtonElement;
  private downloadSvgBtn!: HTMLButtonElement;
  private copyBtn!: HTMLButtonElement;
  private errorModal!: HTMLElement;
  private errorMessage!: HTMLElement;
  private closeModal!: HTMLElement;
  private keyValidation!: HTMLElement;
  private customizationToggle!: HTMLElement;
  private customizationPanel!: HTMLElement;

  constructor() {
    this.unifiedQR = new SimpleUnifiedQRCode();
    this.initializeElements();
    this.setupEventListeners();
    this.setupValidation();
    this.setupMasks();
    this.setupCustomization();
  }

  private initializeElements(): void {
    // Form elements
    this.form = document.getElementById("pixForm") as HTMLFormElement;
    this.keyTypeSelect = document.getElementById("keyType") as HTMLSelectElement;
    this.pixKeyInput = document.getElementById("pixKey") as HTMLInputElement;
    this.receiverNameInput = document.getElementById("receiverName") as HTMLInputElement;
    this.receiverCityInput = document.getElementById("receiverCity") as HTMLInputElement;
    this.amountInput = document.getElementById("amount") as HTMLInputElement;
    this.referenceInput = document.getElementById("reference") as HTMLInputElement;
    this.descriptionInput = document.getElementById("description") as HTMLInputElement;

    // UI elements
    this.generateBtn = document.getElementById("generateBtn") as HTMLButtonElement;
    this.loadingSpinner = document.getElementById("loadingSpinner") as HTMLElement;
    this.qrPlaceholder = document.getElementById("qrPlaceholder") as HTMLElement;
    this.qrResult = document.getElementById("qrResult") as HTMLElement;
    this.qrPreview = document.getElementById("qr-preview") as HTMLElement;
    this.brCodeText = document.getElementById("brCodeText") as HTMLElement;
    this.pixDetails = document.getElementById("pixDetails") as HTMLElement;
    this.downloadBtn = document.getElementById("downloadBtn") as HTMLButtonElement;
    this.downloadSvgBtn = document.getElementById("downloadSvgBtn") as HTMLButtonElement;
    this.copyBtn = document.getElementById("copyBtn") as HTMLButtonElement;

    // Modal elements
    this.errorModal = document.getElementById("errorModal") as HTMLElement;
    this.errorMessage = document.getElementById("errorMessage") as HTMLElement;
    this.closeModal = document.getElementById("closeModal") as HTMLElement;

    // Validation element
    this.keyValidation = document.getElementById("keyValidation") as HTMLElement;

    // Customization elements
    this.customizationToggle = document.getElementById("customizationToggle") as HTMLElement;
    this.customizationPanel = document.getElementById("customizationPanel") as HTMLElement;
  }

  private setupEventListeners(): void {
    // Form submission
    this.form.addEventListener("submit", (e) => this.handleFormSubmit(e));

    // Key type change
    this.keyTypeSelect.addEventListener("change", () => this.handleKeyTypeChange());

    // PIX key input validation
    this.pixKeyInput.addEventListener("input", () => this.validatePixKey());
    this.pixKeyInput.addEventListener("blur", () => this.validatePixKey());

    // Character counters
    this.receiverNameInput.addEventListener("input", () => this.updateCharCounter(this.receiverNameInput, 25));
    this.receiverCityInput.addEventListener("input", () => this.updateCharCounter(this.receiverCityInput, 15));

    // Buttons
    this.downloadBtn.addEventListener("click", () => this.downloadQRCode("png"));
    this.downloadSvgBtn.addEventListener("click", () => this.downloadQRCode("svg"));
    this.copyBtn.addEventListener("click", () => this.copyBRCode());
    this.closeModal.addEventListener("click", () => this.hideModal());

    // Modal close on backdrop click
    this.errorModal.addEventListener("click", (e) => {
      if (e.target === this.errorModal) {
        this.hideModal();
      }
    });

    // ESC key to close modal
    document.addEventListener("keydown", (e) => {
      if (e.key === "Escape" && this.errorModal.style.display === "block") {
        this.hideModal();
      }
    });
  }

  private setupValidation(): void {
    // Initial key type setup
    this.handleKeyTypeChange();
  }

  private setupMasks(): void {
    // Currency mask for amount input
    this.amountInput.addEventListener("input", (e) => {
      const target = e.target as HTMLInputElement;
      let value = target.value.replace(/\D/g, "");
      if (value.length === 0) {
        target.value = "";
        return;
      }

      value = (parseInt(value) / 100).toFixed(2);
      target.value = "R$ " + value.replace(".", ",");
    });

    // Reference input: only alphanumeric
    this.referenceInput.addEventListener("input", (e) => {
      const target = e.target as HTMLInputElement;
      target.value = target.value.replace(/[^A-Za-z0-9]/g, "");
    });
  }

  private setupCustomization(): void {
    // Toggle switch
    this.customizationToggle.addEventListener("click", () => {
      this.customizationToggle.classList.toggle("active");
      this.customizationPanel.classList.toggle("active");
    });

    // Customization controls event listeners
    const customizationInputs = [
      "dots-type",
      "dots-color",
      "corner-square-type",
      "corner-square-color",
      "corner-dot-type",
      "corner-dot-color",
      "background-color",
      "qr-size",
      "image-size",
      "image-margin",
      "hide-background-dots"
    ];

    customizationInputs.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        if ((element as HTMLInputElement).type === "range") {
          element.addEventListener("input", () => this.updateRangeValue(id));
        }

        // Add handler for unified size control
        if (id === "qr-size") {
          element.addEventListener("change", (e) => this.handleSizeChange(e));
        }

        element.addEventListener("change", () => this.generateQRIfReady());
        element.addEventListener("input", () => this.generateQRIfReady());
      }
    });

    // Setup margin buttons
    this.setupMarginButtons();

    // Image upload
    const centerImage = document.getElementById("center-image") as HTMLInputElement;
    if (centerImage) {
      centerImage.addEventListener("change", (e) => this.handleImageUpload(e));
    }

    // Initialize range values
    this.updateRangeValue("image-size");
  }

  private setupMarginButtons(): void {
    const marginButtons = document.querySelectorAll(".margin-btn");
    marginButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        e.preventDefault();

        // Remove active class from all buttons
        marginButtons.forEach((btn) => btn.classList.remove("active"));

        // Add active class to clicked button
        button.classList.add("active");

        // Update container padding based on margin selection
        this.updatePreviewContainerMargin();

        // Regenerate QR code with new margin
        this.generateQRIfReady();
      });
    });
  }

  private updatePreviewContainerMargin(): void {
    const currentMargin = this.getCurrentMargin();

    // Remove existing margin classes
    this.qrPreview.classList.remove("margin-none", "margin-default", "margin-wide");

    // Add appropriate margin class
    switch (currentMargin) {
      case 0:
        this.qrPreview.classList.add("margin-none");
        break;
      case 10:
        this.qrPreview.classList.add("margin-default");
        break;
      case 30:
        this.qrPreview.classList.add("margin-wide");
        break;
    }
  }

  private getCurrentMargin(): number {
    const activeMarginBtn = document.querySelector(".margin-btn.active") as HTMLElement;
    const margin = activeMarginBtn ? parseInt(activeMarginBtn.dataset.margin || "10") : 10;
    return margin;
  }

  private updateRangeValue(inputId: string): void {
    const input = document.getElementById(inputId) as HTMLInputElement;
    if (!input) return;

    let valueElement: HTMLElement | null;
    let displayValue: string;

    switch (inputId) {
      case "image-size":
        valueElement = document.getElementById("image-size-value");
        displayValue = Math.round(parseFloat(input.value) * 100) + "%";
        break;
      default:
        return;
    }

    if (valueElement) {
      valueElement.textContent = displayValue;
    }
  }

  private handleSizeChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const size = parseInt(target.value);
    
    // Atualizar os campos ocultos de largura e altura para manter compatibilidade
    const widthField = document.getElementById("qr-width") as HTMLInputElement;
    const heightField = document.getElementById("qr-height") as HTMLInputElement;
    
    if (widthField) widthField.value = size.toString();
    if (heightField) heightField.value = size.toString();
    
    // Regenerar QR Code se já estiver sendo exibido
    this.generateQRIfReady();
  }

  private handleImageUpload(event: Event): void {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    if (file) {
      this.currentImageFile = file;
      const reader = new FileReader();
      reader.onload = () => {
        this.generateQRIfReady();
      };
      reader.readAsDataURL(file);

      // Update label
      const label = document.querySelector(".image-upload-label");
      if (label) {
        label.innerHTML = `✅ ${file.name}<br><small>Clique para alterar</small>`;
      }
    }
  }

  private handleKeyTypeChange(): void {
    const keyType = this.keyTypeSelect.value;
    const pixKeyInput = this.pixKeyInput;

    // Clear previous value and validation
    pixKeyInput.value = "";
    this.keyValidation.textContent = "";
    this.keyValidation.className = "validation-message";

    // Update placeholder and input type based on key type
    switch (keyType) {
      case "cpf":
        pixKeyInput.placeholder = "000.000.000-00";
        pixKeyInput.type = "text";
        pixKeyInput.maxLength = 14;
        this.setupCPFMask();
        break;
      case "phone":
        pixKeyInput.placeholder = "(11) 99999-9999";
        pixKeyInput.type = "tel";
        pixKeyInput.maxLength = 15;
        this.setupPhoneMask();
        break;
      case "email":
        pixKeyInput.placeholder = "<EMAIL>";
        pixKeyInput.type = "email";
        pixKeyInput.maxLength = 50;
        this.removeMask();
        break;
      case "random":
        pixKeyInput.placeholder = "chave-aleatoria-uuid";
        pixKeyInput.type = "text";
        pixKeyInput.maxLength = 50;
        this.removeMask();
        break;
    }

    // Revalidate if there's already content
    if (pixKeyInput.value.trim()) {
      this.validatePixKey();
    }
  }

  private cpfMaskHandler?: (e: Event) => void;
  private phoneMaskHandler?: (e: Event) => void;

  private setupCPFMask(): void {
    this.cpfMaskHandler = (e) => {
      const target = e.target as HTMLInputElement;
      let value = target.value.replace(/\D/g, "");
      value = value.replace(/(\d{3})(\d)/, "$1.$2");
      value = value.replace(/(\d{3})(\d)/, "$1.$2");
      value = value.replace(/(\d{3})(\d{1,2})$/, "$1-$2");
      target.value = value;
    };
    this.pixKeyInput.addEventListener("input", this.cpfMaskHandler);
  }

  private setupPhoneMask(): void {
    this.phoneMaskHandler = (e) => {
      const target = e.target as HTMLInputElement;
      let value = target.value.replace(/\D/g, "");
      if (value.length <= 10) {
        value = value.replace(/(\d{2})(\d)/, "($1) $2");
        value = value.replace(/(\d{4})(\d)/, "$1-$2");
      } else {
        value = value.replace(/(\d{2})(\d)/, "($1) $2");
        value = value.replace(/(\d{5})(\d)/, "$1-$2");
      }
      target.value = value;
    };
    this.pixKeyInput.addEventListener("input", this.phoneMaskHandler);
  }

  private removeMask(): void {
    if (this.cpfMaskHandler) {
      this.pixKeyInput.removeEventListener("input", this.cpfMaskHandler);
    }
    if (this.phoneMaskHandler) {
      this.pixKeyInput.removeEventListener("input", this.phoneMaskHandler);
    }
  }

  private validatePixKey(): boolean {
    const keyType = this.keyTypeSelect.value;
    const value = this.pixKeyInput.value.trim();
    const validation = this.keyValidation;

    if (!value) {
      validation.textContent = "";
      validation.className = "validation-message";
      return false;
    }

    let isValid = false;
    let message = "";

    switch (keyType) {
      case "cpf":
        isValid = this.unifiedQR.validatePixKey(value);
        message = isValid ? "CPF válido" : "CPF inválido";
        break;
      case "phone":
        isValid = this.unifiedQR.validatePixKey(value);
        message = isValid ? "Telefone válido" : "Telefone inválido";
        break;
      case "email":
        isValid = this.unifiedQR.validatePixKey(value);
        message = isValid ? "Email válido" : "Email inválido";
        break;
      case "random":
        isValid = value.length >= 10;
        message = isValid ? "Chave válida" : "Chave deve ter pelo menos 10 caracteres";
        break;
    }

    validation.textContent = message;
    validation.className = `validation-message ${isValid ? "success" : "error"}`;

    return isValid;
  }

  private updateCharCounter(input: HTMLInputElement, maxLength: number): void {
    const counter = input.parentElement?.querySelector(".char-counter");
    if (counter) {
      counter.textContent = `${input.value.length}/${maxLength} caracteres`;
    }
  }

  private async handleFormSubmit(e: Event): Promise<void> {
    e.preventDefault();

    if (!this.validateForm()) {
      return;
    }

    this.setLoading(true);

    try {
      const formData = this.getFormData();
      const brCode = this.unifiedQR.generatePix(formData);
      await this.generateAndDisplayQRCode(brCode, formData);

      this.displayResult(brCode, formData);
      this.showToast("QR Code PIX gerado com sucesso!", "success");
    } catch (error) {
      console.error("Error generating QR Code:", error);
      this.showError("Erro ao gerar QR Code: " + (error as Error).message);
    } finally {
      this.setLoading(false);
    }
  }

  private validateForm(): boolean {
    const requiredFields = [
      { element: this.pixKeyInput, name: "Chave PIX" },
      { element: this.receiverNameInput, name: "Nome do recebedor" },
      { element: this.receiverCityInput, name: "Cidade do recebedor" }
    ];

    for (const field of requiredFields) {
      if (!field.element.value.trim()) {
        this.showError(`O campo "${field.name}" é obrigatório.`);
        field.element.focus();
        return false;
      }
    }

    // Validate PIX key
    if (!this.validatePixKey()) {
      this.showError("Por favor, insira uma chave PIX válida.");
      this.pixKeyInput.focus();
      return false;
    }

    return true;
  }

  private getFormData(): IPixData {
    // Parse amount
    let amount = 0;
    if (this.amountInput.value.trim()) {
      const amountStr = this.amountInput.value.replace(/[^\d,]/g, "").replace(",", ".");
      amount = parseFloat(amountStr) || 0;
    }

    // Clean PIX key based on type
    let pixKey = this.pixKeyInput.value.trim();
    const keyType = this.keyTypeSelect.value;

    if (keyType === "cpf") {
      pixKey = pixKey.replace(/\D/g, "");
    } else if (keyType === "phone") {
      pixKey = pixKey.replace(/\D/g, "");
      if (!pixKey.startsWith("55")) {
        pixKey = "55" + pixKey;
      }
      if (!pixKey.startsWith("+")) {
        pixKey = "+" + pixKey;
      }
    }

    return {
      key: pixKey,
      nameReceiver: this.receiverNameInput.value.trim(),
      cityReceiver: this.receiverCityInput.value.trim(),
      amount: amount,
      identification: this.referenceInput.value.trim(),
      description: this.descriptionInput.value.trim()
    };
  }

  private async generateAndDisplayQRCode(brCode: string, formData: IPixData): Promise<void> {
    this.currentBRCode = brCode;

    const customizationActive = this.customizationPanel.classList.contains("active");
    const size = parseInt((document.getElementById("qr-size") as HTMLInputElement).value) || 300;
    const margin = this.getCurrentMargin();

    // Build QR code options
    const options: Partial<Options> = {
      data: brCode,
      width: Math.min(size, 350), // Display size limited to 350px
      height: Math.min(size, 350),
      margin: margin,
      type: 'canvas'
    };

    if (customizationActive) {
      // Add customization options
      const dotsType = (document.getElementById("dots-type") as HTMLSelectElement).value;
      const cornerSquareType = (document.getElementById("corner-square-type") as HTMLSelectElement).value;
      const cornerDotType = (document.getElementById("corner-dot-type") as HTMLSelectElement).value;

      options.dotsOptions = {
        type: dotsType as any || "square",
        color: (document.getElementById("dots-color") as HTMLInputElement).value || "#000000"
      };

      options.backgroundOptions = {
        color: (document.getElementById("background-color") as HTMLInputElement).value || "#ffffff"
      };

      options.cornersSquareOptions = {
        type: cornerSquareType as any || undefined,
        color: (document.getElementById("corner-square-color") as HTMLInputElement).value || "#000000"
      };

      options.cornersDotOptions = {
        type: cornerDotType as any || undefined,
        color: (document.getElementById("corner-dot-color") as HTMLInputElement).value || "#000000"
      };

      // Add image if present
      if (this.currentImageFile) {
        const reader = new FileReader();
        return new Promise((resolve) => {
          reader.onload = (e) => {
            options.image = e.target?.result as string;
            options.imageOptions = {
              crossOrigin: "anonymous",
              margin: parseInt((document.getElementById("image-margin") as HTMLInputElement).value) || 0,
              imageSize: parseFloat((document.getElementById("image-size") as HTMLInputElement).value) || 0.4,
              hideBackgroundDots: (document.getElementById("hide-background-dots") as HTMLInputElement).checked
            };

            this.unifiedQR.update(options);
            this.qrPreview.innerHTML = "";
            this.unifiedQR.append(this.qrPreview);
            resolve();
          };
          reader.readAsDataURL(this.currentImageFile!);
        });
      }
    } else {
      // Standard QR code options
      options.dotsOptions = {
        type: "square",
        color: "#000000"
      };

      options.backgroundOptions = {
        color: "#ffffff"
      };

      options.cornersSquareOptions = {
        type: undefined,
        color: "#000000"
      };

      options.cornersDotOptions = {
        type: undefined,
        color: "#000000"
      };
    }

    // Update and display QR code
    this.unifiedQR.update(options);
    this.qrPreview.innerHTML = ""; // Clear previous content
    this.unifiedQR.append(this.qrPreview);
  }

  private generateQRIfReady(): void {
    if (this.currentBRCode) {
      const formData = this.getFormData();
      this.generateAndDisplayQRCode(this.currentBRCode, formData);
    }
  }

  private displayResult(brCode: string, formData: IPixData): void {
    // Hide placeholder and show result
    this.qrPlaceholder.style.display = "none";
    this.qrResult.style.display = "block";

    // Display BR Code
    this.brCodeText.textContent = brCode;

    // Display PIX details
    let detailsHtml = "<h4>Detalhes da Transação:</h4>";
    detailsHtml += `<p><strong>Chave PIX:</strong> ${formData.key}</p>`;
    detailsHtml += `<p><strong>Recebedor:</strong> ${formData.nameReceiver}</p>`;
    detailsHtml += `<p><strong>Cidade:</strong> ${formData.cityReceiver}</p>`;

    if (formData.amount && formData.amount > 0) {
      detailsHtml += `<p><strong>Valor:</strong> R$ ${formData.amount.toFixed(2).replace(".", ",")}</p>`;
    }

    if (formData.identification) {
      detailsHtml += `<p><strong>Referência:</strong> ${formData.identification}</p>`;
    }

    if (formData.description) {
      detailsHtml += `<p><strong>Descrição:</strong> ${formData.description}</p>`;
    }

    this.pixDetails.innerHTML = detailsHtml;
  }

  private async downloadQRCode(format: "png" | "svg"): Promise<void> {
    try {
      const filename = `qr-code-pix.${format}`;
      await this.unifiedQR.download({ name: "qr-code-pix", extension: format });
      this.showToast(`QR Code baixado como ${filename}`, "success");
    } catch (error) {
      console.error("Download error:", error);
      this.showError("Erro ao baixar QR Code: " + (error as Error).message);
    }
  }

  private async copyBRCode(): Promise<void> {
    try {
      await navigator.clipboard.writeText(this.currentBRCode);
      this.showToast("BR Code copiado para a área de transferência!", "success");
    } catch (error) {
      console.error("Copy error:", error);
      this.showError("Erro ao copiar BR Code");
    }
  }

  private setLoading(loading: boolean): void {
    if (loading) {
      this.generateBtn.disabled = true;
      this.loadingSpinner.style.display = "block";
      this.generateBtn.querySelector(".btn-text")!.textContent = "Gerando...";
    } else {
      this.generateBtn.disabled = false;
      this.loadingSpinner.style.display = "none";
      this.generateBtn.querySelector(".btn-text")!.textContent = "Gerar QR Code PIX";
    }
  }

  private showError(message: string): void {
    this.errorMessage.textContent = message;
    this.errorModal.style.display = "block";
  }

  private hideModal(): void {
    this.errorModal.style.display = "none";
  }

  private showToast(message: string, type: "success" | "error"): void {
    // Create toast element
    const toast = document.createElement("div");
    toast.className = `toast toast-${type}`;
    toast.textContent = message;

    // Style the toast
    Object.assign(toast.style, {
      position: "fixed",
      top: "20px",
      right: "20px",
      padding: "12px 24px",
      borderRadius: "8px",
      color: "white",
      backgroundColor: type === "success" ? "#10b981" : "#ef4444",
      zIndex: "10000",
      fontSize: "14px",
      fontWeight: "500",
      boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
      transform: "translateX(100%)",
      transition: "transform 0.3s ease"
    });

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
      toast.style.transform = "translateX(0)";
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      toast.style.transform = "translateX(100%)";
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  }
}
