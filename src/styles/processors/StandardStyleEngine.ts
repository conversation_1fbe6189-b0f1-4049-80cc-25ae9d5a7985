import { IStyleEngine } from "../../core/interfaces/IStyleEngine";
import { Options, Gradient } from "../../types";

/**
 * Standard style engine implementation
 * Implements Single Responsibility Principle - handles basic styling operations
 */
export class StandardStyleEngine implements IStyleEngine {
  /**
   * Applies styles to element
   * @param element Element to style
   * @param options Style options
   */
  applyStyles(element: any, options: Options): void {
    if (!element || !options) {
      return;
    }

    // Apply dot styles
    if (options.dotsOptions) {
      this.applyDotStyles(element, options.dotsOptions);
    }

    // Apply corner styles
    if (options.cornersSquareOptions) {
      this.applyCornerSquareStyles(element, options.cornersSquareOptions);
    }

    if (options.cornersDotOptions) {
      this.applyCornerDotStyles(element, options.cornersDotOptions);
    }

    // Apply background styles
    if (options.backgroundOptions) {
      this.applyBackgroundStyles(element, options.backgroundOptions);
    }
  }

  /**
   * Processes gradient configuration
   * @param gradient Gradient configuration
   * @returns Processed gradient definition
   */
  processGradient(gradient: Gradient): any {
    if (!gradient || !gradient.colorStops || gradient.colorStops.length < 2) {
      return null;
    }

    return {
      type: gradient.type || 'linear',
      rotation: gradient.rotation || 0,
      colorStops: gradient.colorStops.map(stop => ({
        offset: Math.max(0, Math.min(1, stop.offset)),
        color: this.validateColor(stop.color)
      }))
    };
  }

  /**
   * Validates style options
   * @param options Options to validate
   * @returns true if valid
   */
  validateOptions(options: Options): boolean {
    if (!options) {
      return false;
    }

    // Validate dot options
    if (options.dotsOptions) {
      if (!this.validateDotOptions(options.dotsOptions)) {
        return false;
      }
    }

    // Validate corner options
    if (options.cornersSquareOptions && !this.validateCornerOptions(options.cornersSquareOptions)) {
      return false;
    }

    if (options.cornersDotOptions && !this.validateCornerOptions(options.cornersDotOptions)) {
      return false;
    }

    // Validate background options
    if (options.backgroundOptions && !this.validateBackgroundOptions(options.backgroundOptions)) {
      return false;
    }

    return true;
  }

  /**
   * Applies dot styles
   * @param element Element to style
   * @param dotOptions Dot style options
   */
  private applyDotStyles(element: any, dotOptions: any): void {
    if (dotOptions.color) {
      this.setElementColor(element, dotOptions.color);
    }

    if (dotOptions.gradient) {
      const processedGradient = this.processGradient(dotOptions.gradient);
      if (processedGradient) {
        this.setElementGradient(element, processedGradient);
      }
    }
  }

  /**
   * Applies corner square styles
   * @param element Element to style
   * @param cornerOptions Corner style options
   */
  private applyCornerSquareStyles(element: any, cornerOptions: any): void {
    if (cornerOptions.color) {
      this.setElementColor(element, cornerOptions.color);
    }

    if (cornerOptions.gradient) {
      const processedGradient = this.processGradient(cornerOptions.gradient);
      if (processedGradient) {
        this.setElementGradient(element, processedGradient);
      }
    }
  }

  /**
   * Applies corner dot styles
   * @param element Element to style
   * @param cornerOptions Corner dot style options
   */
  private applyCornerDotStyles(element: any, cornerOptions: any): void {
    if (cornerOptions.color) {
      this.setElementColor(element, cornerOptions.color);
    }

    if (cornerOptions.gradient) {
      const processedGradient = this.processGradient(cornerOptions.gradient);
      if (processedGradient) {
        this.setElementGradient(element, processedGradient);
      }
    }
  }

  /**
   * Applies background styles
   * @param element Element to style
   * @param backgroundOptions Background style options
   */
  private applyBackgroundStyles(element: any, backgroundOptions: any): void {
    if (backgroundOptions.color) {
      this.setElementBackgroundColor(element, backgroundOptions.color);
    }

    if (backgroundOptions.gradient) {
      const processedGradient = this.processGradient(backgroundOptions.gradient);
      if (processedGradient) {
        this.setElementBackgroundGradient(element, processedGradient);
      }
    }
  }

  /**
   * Sets element color
   * @param element Element to style
   * @param color Color value
   */
  private setElementColor(element: any, color: string): void {
    if (element.setAttribute) {
      element.setAttribute('fill', color);
    } else if (element.style) {
      element.style.color = color;
    }
  }

  /**
   * Sets element gradient
   * @param element Element to style
   * @param gradient Processed gradient
   */
  private setElementGradient(element: any, gradient: any): void {
    // This would create and apply gradient definitions
    // Implementation depends on the rendering context (SVG, Canvas, etc.)
    console.warn('Gradient application not fully implemented');
  }

  /**
   * Sets element background color
   * @param element Element to style
   * @param color Background color
   */
  private setElementBackgroundColor(element: any, color: string): void {
    if (element.style) {
      element.style.backgroundColor = color;
    }
  }

  /**
   * Sets element background gradient
   * @param element Element to style
   * @param gradient Processed gradient
   */
  private setElementBackgroundGradient(element: any, gradient: any): void {
    // This would create and apply background gradient
    console.warn('Background gradient application not fully implemented');
  }

  /**
   * Validates color value
   * @param color Color to validate
   * @returns Validated color
   */
  private validateColor(color: string): string {
    if (!color || typeof color !== 'string') {
      return '#000000';
    }

    // Basic color validation
    if (color.startsWith('#') && /^#[0-9A-Fa-f]{6}$/.test(color)) {
      return color;
    }

    if (color.startsWith('#') && /^#[0-9A-Fa-f]{3}$/.test(color)) {
      // Convert 3-digit hex to 6-digit
      return '#' + color[1] + color[1] + color[2] + color[2] + color[3] + color[3];
    }

    // CSS color names, rgb(), rgba(), hsl(), etc. would be validated here
    return color; // Return as-is for now
  }

  /**
   * Validates dot options
   * @param dotOptions Dot options to validate
   * @returns true if valid
   */
  private validateDotOptions(dotOptions: any): boolean {
    if (dotOptions.color && !this.isValidColor(dotOptions.color)) {
      return false;
    }

    if (dotOptions.gradient && !this.isValidGradient(dotOptions.gradient)) {
      return false;
    }

    return true;
  }

  /**
   * Validates corner options
   * @param cornerOptions Corner options to validate
   * @returns true if valid
   */
  private validateCornerOptions(cornerOptions: any): boolean {
    if (cornerOptions.color && !this.isValidColor(cornerOptions.color)) {
      return false;
    }

    if (cornerOptions.gradient && !this.isValidGradient(cornerOptions.gradient)) {
      return false;
    }

    return true;
  }

  /**
   * Validates background options
   * @param backgroundOptions Background options to validate
   * @returns true if valid
   */
  private validateBackgroundOptions(backgroundOptions: any): boolean {
    if (backgroundOptions.color && !this.isValidColor(backgroundOptions.color)) {
      return false;
    }

    if (backgroundOptions.gradient && !this.isValidGradient(backgroundOptions.gradient)) {
      return false;
    }

    return true;
  }

  /**
   * Checks if color is valid
   * @param color Color to check
   * @returns true if valid
   */
  private isValidColor(color: string): boolean {
    if (!color || typeof color !== 'string') {
      return false;
    }

    // Basic validation - could be more comprehensive
    return /^#[0-9A-Fa-f]{3,6}$/.test(color) || 
           color.startsWith('rgb') || 
           color.startsWith('hsl') ||
           ['transparent', 'inherit', 'currentColor'].includes(color);
  }

  /**
   * Checks if gradient is valid
   * @param gradient Gradient to check
   * @returns true if valid
   */
  private isValidGradient(gradient: any): boolean {
    if (!gradient || typeof gradient !== 'object') {
      return false;
    }

    if (!['linear', 'radial'].includes(gradient.type)) {
      return false;
    }

    if (!Array.isArray(gradient.colorStops) || gradient.colorStops.length < 2) {
      return false;
    }

    return gradient.colorStops.every((stop: any) => 
      typeof stop.offset === 'number' && 
      stop.offset >= 0 && 
      stop.offset <= 1 &&
      this.isValidColor(stop.color)
    );
  }
}
