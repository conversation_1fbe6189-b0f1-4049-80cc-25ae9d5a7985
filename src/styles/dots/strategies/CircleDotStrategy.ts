import { DrawArgs } from "../../../types";
import { BaseDotDrawingStrategy, DotDrawingContext } from "../DotDrawingStrategy";

/**
 * Strategy for drawing circular dots
 * Implements Single Responsibility Principle - only handles circular dot drawing
 */
export class CircleDotStrategy extends BaseDotDrawingStrategy {
  /**
   * Draws a circular dot
   * @param args Drawing arguments
   * @param context Drawing context
   */
  draw(args: DrawArgs, context: DotDrawingContext): void {
    const { x, y, size } = args;
    this.basicDot({ x, y, size, rotation: 0 }, context);
  }

  /**
   * Gets the strategy name
   * @returns Strategy name
   */
  getName(): string {
    return 'dots';
  }
}
