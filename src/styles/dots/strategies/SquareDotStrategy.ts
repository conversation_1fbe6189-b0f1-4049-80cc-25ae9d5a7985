import { DrawArgs } from "../../../types";
import { BaseDotDrawingStrategy, DotDrawingContext } from "../DotDrawingStrategy";

/**
 * Strategy for drawing square dots
 * Implements Single Responsibility Principle - only handles square dot drawing
 */
export class SquareDotStrategy extends BaseDotDrawingStrategy {
  /**
   * Draws a square dot
   * @param args Drawing arguments
   * @param context Drawing context
   */
  draw(args: DrawArgs, context: DotDrawingContext): void {
    const { x, y, size } = args;
    this.basicSquare({ x, y, size, rotation: 0 }, context);
  }

  /**
   * Gets the strategy name
   * @returns Strategy name
   */
  getName(): string {
    return 'square';
  }
}
