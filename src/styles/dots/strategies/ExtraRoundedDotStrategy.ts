import { DrawArgs } from "../../../types";
import { BaseDotDrawingStrategy, DotDrawingContext } from "../DotDrawingStrategy";

/**
 * Strategy for drawing extra rounded dots
 * Implements Single Responsibility Principle - only handles extra rounded dot drawing
 */
export class ExtraRoundedDotStrategy extends BaseDotDrawingStrategy {
  /**
   * Draws an extra rounded dot based on neighbors
   * @param args Drawing arguments
   * @param context Drawing context
   */
  draw(args: DrawArgs, context: DotDrawingContext): void {
    const { x, y, size, getNeighbor } = args;
    const neighbors = this.analyzeNeighbors(getNeighbor);

    // No neighbors - draw circle
    if (neighbors.count === 0) {
      this.basicDot({ x, y, size, rotation: 0 }, context);
      return;
    }

    // More than 2 neighbors or opposite neighbors - draw square
    if (neighbors.count > 2 || 
        (neighbors.left && neighbors.right) || 
        (neighbors.top && neighbors.bottom)) {
      this.basicSquare({ x, y, size, rotation: 0 }, context);
      return;
    }

    // Single neighbor - draw side rounded
    if (neighbors.count === 1) {
      if (neighbors.top) {
        this.basicSideRounded({ x, y, size, rotation: -Math.PI / 2 }, context);
      } else if (neighbors.right) {
        this.basicSideRounded({ x, y, size, rotation: 0 }, context);
      } else if (neighbors.bottom) {
        this.basicSideRounded({ x, y, size, rotation: Math.PI / 2 }, context);
      } else if (neighbors.left) {
        this.basicSideRounded({ x, y, size, rotation: Math.PI }, context);
      }
      return;
    }

    // Two adjacent neighbors - draw extra rounded corner
    if (neighbors.count === 2) {
      if (neighbors.top && neighbors.right) {
        this.basicCornerExtraRounded({ x, y, size, rotation: 0 }, context);
      } else if (neighbors.right && neighbors.bottom) {
        this.basicCornerExtraRounded({ x, y, size, rotation: Math.PI / 2 }, context);
      } else if (neighbors.bottom && neighbors.left) {
        this.basicCornerExtraRounded({ x, y, size, rotation: Math.PI }, context);
      } else if (neighbors.left && neighbors.top) {
        this.basicCornerExtraRounded({ x, y, size, rotation: -Math.PI / 2 }, context);
      }
    }
  }

  /**
   * Gets the strategy name
   * @returns Strategy name
   */
  getName(): string {
    return 'extra-rounded';
  }
}
