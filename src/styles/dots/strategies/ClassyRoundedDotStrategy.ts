import { DrawArgs } from "../../../types";
import { BaseDotDrawingStrategy, DotDrawingContext } from "../DotDrawingStrategy";

/**
 * Strategy for drawing classy rounded dots
 * Implements Single Responsibility Principle - only handles classy rounded dot drawing
 */
export class ClassyRoundedDotStrategy extends BaseDotDrawingStrategy {
  /**
   * Draws a classy rounded dot based on neighbors
   * @param args Drawing arguments
   * @param context Drawing context
   */
  draw(args: DrawArgs, context: DotDrawingContext): void {
    const { x, y, size, getNeighbor } = args;
    const neighbors = this.analyzeNeighbors(getNeighbor);

    // No neighbors - draw corners rounded
    if (neighbors.count === 0) {
      this.basicCornersRounded({ x, y, size, rotation: Math.PI / 2 }, context);
      return;
    }

    // Special corner cases with extra rounding
    if (!neighbors.left && !neighbors.top) {
      this.basicCornerExtraRounded({ x, y, size, rotation: -Math.PI / 2 }, context);
      return;
    }

    if (!neighbors.right && !neighbors.bottom) {
      this.basicCornerExtraRounded({ x, y, size, rotation: Math.PI / 2 }, context);
      return;
    }

    if (!neighbors.right && !neighbors.top) {
      this.basicCornerExtraRounded({ x, y, size, rotation: 0 }, context);
      return;
    }

    if (!neighbors.left && !neighbors.bottom) {
      this.basicCornerExtraRounded({ x, y, size, rotation: Math.PI }, context);
      return;
    }

    // Default to square for other cases
    this.basicSquare({ x, y, size, rotation: 0 }, context);
  }

  /**
   * Gets the strategy name
   * @returns Strategy name
   */
  getName(): string {
    return 'classy-rounded';
  }
}
