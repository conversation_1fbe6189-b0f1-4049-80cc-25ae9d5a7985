import { DrawArgs } from "../../../types";
import { BaseDotDrawingStrategy, DotDrawingContext } from "../DotDrawingStrategy";

/**
 * Strategy for drawing classy dots
 * Implements Single Responsibility Principle - only handles classy dot drawing
 */
export class ClassyDotStrategy extends BaseDotDrawingStrategy {
  /**
   * Draws a classy dot based on neighbors
   * @param args Drawing arguments
   * @param context Drawing context
   */
  draw(args: DrawArgs, context: DotDrawingContext): void {
    const { x, y, size, getNeighbor } = args;
    const neighbors = this.analyzeNeighbors(getNeighbor);

    // No neighbors - draw corners rounded
    if (neighbors.count === 0) {
      this.basicCornersRounded({ x, y, size, rotation: Math.PI / 2 }, context);
      return;
    }

    // Special corner cases
    if (!neighbors.left && !neighbors.top) {
      this.basicCornerRounded({ x, y, size, rotation: -Math.PI / 2 }, context);
      return;
    }

    if (!neighbors.right && !neighbors.bottom) {
      this.basicCornerRounded({ x, y, size, rotation: Math.PI / 2 }, context);
      return;
    }

    if (!neighbors.right && !neighbors.top) {
      this.basicCornerRounded({ x, y, size, rotation: 0 }, context);
      return;
    }

    if (!neighbors.left && !neighbors.bottom) {
      this.basicCornerRounded({ x, y, size, rotation: Math.PI }, context);
      return;
    }

    // Default to square for other cases
    this.basicSquare({ x, y, size, rotation: 0 }, context);
  }

  /**
   * Gets the strategy name
   * @returns Strategy name
   */
  getName(): string {
    return 'classy';
  }
}
