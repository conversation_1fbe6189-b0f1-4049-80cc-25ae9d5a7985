import { IStyleProcessor, IStyleContext } from "../../core/interfaces/IStyleEngine";
import { DotType } from "../../types";
import { DotStrategyFactory } from "./DotStrategyFactory";

/**
 * Processes dot-specific styles
 * Implements Single Responsibility Principle - only handles dot style processing
 */
export class DotStyleProcessor implements IStyleProcessor {
  /**
   * Processes dot style
   * @param value Style value
   * @param context Style context
   * @returns Processed style
   */
  process(value: any, context: IStyleContext): any {
    if (!this.canProcess('dot')) {
      throw new Error('Cannot process dot style');
    }

    const dotOptions = value as {
      type?: DotType;
      color?: string;
      gradient?: any;
      roundSize?: boolean;
    };

    // Validate dot type
    const validatedType = DotStrategyFactory.validateType(dotOptions.type || 'square');

    return {
      ...dotOptions,
      type: validatedType,
      processedAt: new Date().toISOString(),
      context: {
        moduleSize: context.moduleSize,
        position: context.position
      }
    };
  }

  /**
   * Checks if can process the style type
   * @param type Style type
   * @returns true if can process
   */
  canProcess(type: string): boolean {
    return type === 'dot' || type === 'dots';
  }

  /**
   * Gets supported dot types
   * @returns Array of supported dot types
   */
  getSupportedDotTypes(): string[] {
    return DotStrategyFactory.getAvailableTypes();
  }

  /**
   * Validates dot options
   * @param options Dot options to validate
   * @returns Validation result
   */
  validateDotOptions(options: any): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (options.type && !DotStrategyFactory.isSupported(options.type)) {
      warnings.push(`Dot type '${options.type}' is not supported, will use default`);
    }

    if (options.color && typeof options.color !== 'string') {
      errors.push('Dot color must be a string');
    }

    if (options.roundSize && typeof options.roundSize !== 'boolean') {
      errors.push('roundSize must be a boolean');
    }

    if (options.gradient) {
      if (!options.gradient.type || !['linear', 'radial'].includes(options.gradient.type)) {
        errors.push('Gradient type must be "linear" or "radial"');
      }

      if (!Array.isArray(options.gradient.colorStops) || options.gradient.colorStops.length < 2) {
        errors.push('Gradient must have at least 2 color stops');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Applies default values to dot options
   * @param options Partial dot options
   * @returns Complete dot options with defaults
   */
  applyDefaults(options: Partial<any>): any {
    return {
      type: 'square',
      color: '#000000',
      roundSize: true,
      ...options
    };
  }

  /**
   * Optimizes dot options for performance
   * @param options Dot options
   * @returns Optimized options
   */
  optimize(options: any): any {
    const optimized = { ...options };

    // Remove unnecessary properties
    if (optimized.gradient && optimized.color) {
      // If gradient is present, color is not needed
      delete optimized.color;
    }

    // Optimize gradient color stops
    if (optimized.gradient?.colorStops) {
      optimized.gradient.colorStops = optimized.gradient.colorStops
        .filter((stop: any) => stop.offset >= 0 && stop.offset <= 1)
        .sort((a: any, b: any) => a.offset - b.offset);
    }

    return optimized;
  }

  /**
   * Converts dot options to CSS-compatible format
   * @param options Dot options
   * @returns CSS-compatible options
   */
  toCssFormat(options: any): Record<string, string> {
    const css: Record<string, string> = {};

    if (options.color) {
      css.fill = options.color;
    }

    if (options.gradient) {
      // This would be handled by the gradient processor
      css['data-gradient'] = JSON.stringify(options.gradient);
    }

    return css;
  }
}
