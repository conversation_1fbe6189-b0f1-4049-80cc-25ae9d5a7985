import { DotType, Window } from "../../types";
import { IDotDrawingStrategy } from "./DotDrawingStrategy";
import { SquareDotStrategy } from "./strategies/SquareDotStrategy";
import { CircleDotStrategy } from "./strategies/CircleDotStrategy";
import { RoundedDotStrategy } from "./strategies/RoundedDotStrategy";
import { ExtraRoundedDotStrategy } from "./strategies/ExtraRoundedDotStrategy";
import { ClassyDotStrategy } from "./strategies/ClassyDotStrategy";
import { ClassyRoundedDotStrategy } from "./strategies/ClassyRoundedDotStrategy";

/**
 * Factory for creating dot drawing strategies
 * Implements Factory Pattern and Strategy Pattern
 */
export class DotStrategyFactory {
  private static strategies = new Map<string, new (window: Window) => IDotDrawingStrategy>();

  static {
    // Register default strategies
    this.register('square', SquareDotStrategy);
    this.register('dots', CircleDotStrategy);
    this.register('rounded', RoundedDotStrategy);
    this.register('extra-rounded', ExtraRoundedDotStrategy);
    this.register('classy', ClassyDotStrategy);
    this.register('classy-rounded', ClassyRoundedDotStrategy);
  }

  /**
   * Creates a dot drawing strategy
   * @param type Dot type
   * @param window Window object
   * @returns Dot drawing strategy
   */
  static create(type: DotType, window: Window): IDotDrawingStrategy {
    const StrategyClass = this.strategies.get(type);
    
    if (!StrategyClass) {
      // Default to square strategy
      const DefaultStrategy = this.strategies.get('square');
      if (!DefaultStrategy) {
        throw new Error('Default square strategy not found');
      }
      return new DefaultStrategy(window);
    }

    return new StrategyClass(window);
  }

  /**
   * Registers a new dot strategy
   * @param type Dot type name
   * @param strategyClass Strategy class constructor
   */
  static register(type: string, strategyClass: new (window: Window) => IDotDrawingStrategy): void {
    this.strategies.set(type, strategyClass);
  }

  /**
   * Unregisters a dot strategy
   * @param type Dot type name
   */
  static unregister(type: string): void {
    this.strategies.delete(type);
  }

  /**
   * Gets all available dot types
   * @returns Array of dot type names
   */
  static getAvailableTypes(): string[] {
    return Array.from(this.strategies.keys());
  }

  /**
   * Checks if a dot type is supported
   * @param type Dot type name
   * @returns true if supported
   */
  static isSupported(type: string): boolean {
    return this.strategies.has(type);
  }

  /**
   * Gets the default dot type
   * @returns Default dot type
   */
  static getDefaultType(): string {
    return 'square';
  }

  /**
   * Validates dot type
   * @param type Dot type to validate
   * @returns Validated dot type (or default if invalid)
   */
  static validateType(type: string): string {
    return this.isSupported(type) ? type : this.getDefaultType();
  }
}
