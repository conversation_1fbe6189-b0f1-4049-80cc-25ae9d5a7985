import { DrawArgs, BasicFigureDrawArgs, RotateFigureArgs, Window } from "../../types";

/**
 * Interface for dot drawing strategies
 * Implements Strategy Pattern for different dot types
 */
export interface IDotDrawingStrategy {
  /**
   * Draws a dot with the specific strategy
   * @param args Drawing arguments
   * @param context Drawing context
   */
  draw(args: DrawArgs, context: DotDrawingContext): void;

  /**
   * Gets the strategy name
   * @returns Strategy name
   */
  getName(): string;
}

/**
 * Context for dot drawing operations
 */
export interface DotDrawingContext {
  window: Window;
  svg: SVGElement;
  element?: SVGElement;
  setElement(element: SVGElement): void;
}

/**
 * Base class for dot drawing strategies
 * Provides common functionality for all dot types
 */
export abstract class BaseDotDrawingStrategy implements IDotDrawingStrategy {
  protected window: Window;

  constructor(window: Window) {
    this.window = window;
  }

  abstract draw(args: DrawArgs, context: DotDrawingContext): void;
  abstract getName(): string;

  /**
   * Rotates a figure around its center
   * @param args Rotation arguments
   * @param context Drawing context
   */
  protected rotateFigure(args: RotateFigureArgs, context: DotDrawingContext): void {
    const { x, y, size, rotation = 0, draw } = args;
    const cx = x + size / 2;
    const cy = y + size / 2;

    draw();
    context.element?.setAttribute("transform", `rotate(${(180 * rotation) / Math.PI},${cx},${cy})`);
  }

  /**
   * Creates a basic dot (circle)
   * @param args Drawing arguments
   * @param context Drawing context
   */
  protected basicDot(args: BasicFigureDrawArgs, context: DotDrawingContext): void {
    const { size, x, y } = args;

    this.rotateFigure({
      ...args,
      draw: () => {
        const element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "circle");
        element.setAttribute("cx", String(x + size / 2));
        element.setAttribute("cy", String(y + size / 2));
        element.setAttribute("r", String(size / 2));
        context.setElement(element);
      }
    }, context);
  }

  /**
   * Creates a basic square
   * @param args Drawing arguments
   * @param context Drawing context
   */
  protected basicSquare(args: BasicFigureDrawArgs, context: DotDrawingContext): void {
    const { size, x, y } = args;

    this.rotateFigure({
      ...args,
      draw: () => {
        const element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "rect");
        element.setAttribute("x", String(x));
        element.setAttribute("y", String(y));
        element.setAttribute("width", String(size));
        element.setAttribute("height", String(size));
        context.setElement(element);
      }
    }, context);
  }

  /**
   * Creates a side rounded shape
   * @param args Drawing arguments
   * @param context Drawing context
   */
  protected basicSideRounded(args: BasicFigureDrawArgs, context: DotDrawingContext): void {
    const { size, x, y } = args;

    this.rotateFigure({
      ...args,
      draw: () => {
        const element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
        element.setAttribute(
          "d",
          `M ${x} ${y}` + //go to top left position
            `v ${size}` + //draw line to left bottom corner
            `h ${size / 2}` + //draw line to left bottom corner + half of size right
            `a ${size / 2} ${size / 2}, 0, 0, 0, 0 ${-size}` // draw rounded corner
        );
        context.setElement(element);
      }
    }, context);
  }

  /**
   * Creates a corner rounded shape
   * @param args Drawing arguments
   * @param context Drawing context
   */
  protected basicCornerRounded(args: BasicFigureDrawArgs, context: DotDrawingContext): void {
    const { size, x, y } = args;

    this.rotateFigure({
      ...args,
      draw: () => {
        const element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
        element.setAttribute(
          "d",
          `M ${x} ${y}` + //go to top left position
            `v ${size}` + //draw line to left bottom corner
            `h ${size}` + //draw line to right bottom corner
            `v ${-size / 2}` + //draw line to right bottom corner + half of size top
            `a ${size / 2} ${size / 2}, 0, 0, 0, ${-size / 2} ${-size / 2}` // draw rounded corner
        );
        context.setElement(element);
      }
    }, context);
  }

  /**
   * Creates an extra rounded corner shape
   * @param args Drawing arguments
   * @param context Drawing context
   */
  protected basicCornerExtraRounded(args: BasicFigureDrawArgs, context: DotDrawingContext): void {
    const { size, x, y } = args;

    this.rotateFigure({
      ...args,
      draw: () => {
        const element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
        element.setAttribute(
          "d",
          `M ${x} ${y}` + //go to top left position
            `v ${size}` + //draw line to left bottom corner
            `h ${size}` + //draw line to right bottom corner
            `a ${size} ${size}, 0, 0, 0, ${-size} ${-size}` // draw rounded top right corner
        );
        context.setElement(element);
      }
    }, context);
  }

  /**
   * Creates a shape with two corners rounded
   * @param args Drawing arguments
   * @param context Drawing context
   */
  protected basicCornersRounded(args: BasicFigureDrawArgs, context: DotDrawingContext): void {
    const { size, x, y } = args;

    this.rotateFigure({
      ...args,
      draw: () => {
        const element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "path");
        element.setAttribute(
          "d",
          `M ${x} ${y}` + //go to left top position
            `v ${size / 2}` + //draw line to left top corner + half of size bottom
            `a ${size / 2} ${size / 2}, 0, 0, 0, ${size / 2} ${size / 2}` + // draw rounded left bottom corner
            `h ${size / 2}` + //draw line to right bottom corner
            `v ${-size / 2}` + //draw line to right bottom corner + half of size top
            `a ${size / 2} ${size / 2}, 0, 0, 0, ${-size / 2} ${-size / 2}` // draw rounded right top corner
        );
        context.setElement(element);
      }
    }, context);
  }

  /**
   * Analyzes neighbors to determine drawing strategy
   * @param getNeighbor Neighbor checking function
   * @returns Neighbor analysis
   */
  protected analyzeNeighbors(getNeighbor?: (x: number, y: number) => boolean): {
    left: number;
    right: number;
    top: number;
    bottom: number;
    count: number;
  } {
    const left = getNeighbor ? +getNeighbor(-1, 0) : 0;
    const right = getNeighbor ? +getNeighbor(1, 0) : 0;
    const top = getNeighbor ? +getNeighbor(0, -1) : 0;
    const bottom = getNeighbor ? +getNeighbor(0, 1) : 0;

    return {
      left,
      right,
      top,
      bottom,
      count: left + right + top + bottom
    };
  }
}
