import { DotType, GetNeighbor, Window } from "../../types";
import { IDotDrawingStrategy, DotDrawingContext } from "./DotDrawingStrategy";
import { DotStrategyFactory } from "./DotStrategyFactory";

/**
 * Refactored QR Dot class using Strategy Pattern
 * Implements Single Responsibility Principle - only manages dot drawing coordination
 */
export class QRDotRefactored implements DotDrawingContext {
  private strategy: IDotDrawingStrategy;
  public svg: SVGElement;
  public window: Window;
  public element?: SVGElement;

  constructor({ svg, type, window }: { svg: SVGElement; type: DotType; window: Window }) {
    this.svg = svg;
    this.window = window;
    this.strategy = DotStrategyFactory.create(type, window);
  }

  /**
   * Draws a dot using the current strategy
   * @param x X position
   * @param y Y position
   * @param size Dot size
   * @param getNeighbor Neighbor checking function
   */
  draw(x: number, y: number, size: number, getNeighbor: GetNeighbor): void {
    this.strategy.draw({ x, y, size, getNeighbor }, this);
  }

  /**
   * Changes the drawing strategy
   * @param type New dot type
   */
  changeStrategy(type: DotType): void {
    this.strategy = DotStrategyFactory.create(type, this.window);
  }

  /**
   * Gets the current strategy name
   * @returns Strategy name
   */
  getStrategyName(): string {
    return this.strategy.getName();
  }

  /**
   * Sets the current element (DotDrawingContext implementation)
   * @param element SVG element
   */
  setElement(element: SVGElement): void {
    this.element = element;
  }

  /**
   * Gets the current element
   * @returns Current SVG element
   */
  getElement(): SVGElement | undefined {
    return this.element;
  }

  /**
   * Gets the SVG container
   * @returns SVG container
   */
  getSvg(): SVGElement {
    return this.svg;
  }

  /**
   * Gets the window object
   * @returns Window object
   */
  getWindow(): Window {
    return this.window;
  }

  /**
   * Clears the current element
   */
  clear(): void {
    this.element = undefined;
  }

  /**
   * Creates a preview of the dot without adding it to the DOM
   * @param x X position
   * @param y Y position
   * @param size Dot size
   * @param getNeighbor Neighbor checking function
   * @returns Preview element
   */
  createPreview(x: number, y: number, size: number, getNeighbor: GetNeighbor): SVGElement | undefined {
    const originalElement = this.element;
    
    // Draw to get the element
    this.draw(x, y, size, getNeighbor);
    const previewElement = this.element;
    
    // Restore original state
    this.element = originalElement;
    
    return previewElement;
  }

  /**
   * Validates drawing parameters
   * @param x X position
   * @param y Y position
   * @param size Dot size
   * @returns Validation result
   */
  validateDrawingParams(x: number, y: number, size: number): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (typeof x !== 'number' || isNaN(x)) {
      errors.push('X position must be a valid number');
    }

    if (typeof y !== 'number' || isNaN(y)) {
      errors.push('Y position must be a valid number');
    }

    if (typeof size !== 'number' || isNaN(size) || size <= 0) {
      errors.push('Size must be a positive number');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Gets drawing statistics
   * @returns Drawing statistics
   */
  getStats(): {
    strategyName: string;
    hasElement: boolean;
    elementType?: string;
  } {
    return {
      strategyName: this.getStrategyName(),
      hasElement: !!this.element,
      elementType: this.element?.tagName
    };
  }
}
