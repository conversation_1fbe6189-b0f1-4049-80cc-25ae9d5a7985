import { Base<PERSON>enderer } from "../base/BaseRenderer";
import { IRendererConfig } from "../../core/interfaces/IQRRenderer";
import { QRCode, Options } from "../../types";
import { Canvas as NodeCanvas } from "canvas";

/**
 * Canvas-based QR Code renderer
 * Implements Single Responsibility Principle - only handles Canvas rendering
 */
export class CanvasRenderer extends BaseRenderer {
  private canvas?: HTMLCanvasElement | NodeCanvas;
  private ctx?: CanvasRenderingContext2D;

  constructor(config: IRendererConfig) {
    super(config);
  }

  /**
   * Checks if renderer supports the specified type
   * @param type Renderer type
   * @returns true if supported
   */
  supports(type: string): boolean {
    return type.toLowerCase() === 'canvas';
  }

  /**
   * Creates the canvas element
   */
  protected createElement(): void {
    if (typeof window !== 'undefined' && window.document) {
      // Browser environment
      this.canvas = document.createElement('canvas');
    } else {
      // Node.js environment - requires node-canvas
      const { createCanvas } = require('canvas');
      this.canvas = createCanvas(this.config.width, this.config.height);
    }

    this.element = this.canvas as HTMLCanvasElement;
  }

  /**
   * Sets up canvas properties
   */
  protected setupElement(): void {
    if (!this.canvas) {
      throw new Error('Canvas element not created');
    }

    this.canvas.width = this.config.width;
    this.canvas.height = this.config.height;

    const ctx = this.canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Failed to get 2D context from canvas');
    }
    this.ctx = ctx as CanvasRenderingContext2D;

    // Set default styles
    this.ctx.fillStyle = '#000000';
    this.ctx.strokeStyle = '#000000';
  }

  /**
   * Renders QR modules on canvas
   * @param qr QR Code instance
   * @param options Rendering options
   */
  protected async renderModules(qr: QRCode, options: Options): Promise<void> {
    if (!this.ctx || !this.context) {
      throw new Error('Canvas context not initialized');
    }

    // Clear canvas with background color
    this.ctx.fillStyle = options.backgroundOptions?.color || '#ffffff';
    this.ctx.fillRect(0, 0, this.config.width, this.config.height);

    // Set module color
    this.ctx.fillStyle = options.dotsOptions?.color || '#000000';

    const moduleCount = qr.getModuleCount();

    // Render each module
    for (let row = 0; row < moduleCount; row++) {
      for (let col = 0; col < moduleCount; col++) {
        if (qr.isDark(row, col)) {
          await this.renderModule(row, col, qr, options);
        }
      }
    }
  }

  /**
   * Renders a single module
   * @param row Module row
   * @param col Module column
   * @param qr QR Code instance
   * @param options Rendering options
   */
  private async renderModule(row: number, col: number, qr: QRCode, options: Options): Promise<void> {
    if (!this.ctx || !this.context) return;

    const position = this.getModulePosition(row, col);
    const size = this.context.moduleSize;

    // Check if this is a special area (corners)
    if (this.isCornerSquare(row, col)) {
      await this.renderCornerSquare(position, size, options);
    } else if (this.isCornerDot(row, col)) {
      await this.renderCornerDot(position, size, options);
    } else {
      await this.renderDot(position, size, row, col, qr, options);
    }
  }

  /**
   * Renders a corner square
   * @param position Module position
   * @param size Module size
   * @param options Rendering options
   */
  private async renderCornerSquare(position: { x: number; y: number }, size: number, options: Options): Promise<void> {
    if (!this.ctx) return;

    const cornerType = options.cornersSquareOptions?.type || 'square';
    const color = options.cornersSquareOptions?.color || options.dotsOptions?.color || '#000000';

    this.ctx.fillStyle = color;

    switch (cornerType) {
      case 'square':
        this.ctx.fillRect(position.x, position.y, size, size);
        break;
      case 'extra-rounded':
        this.drawRoundedRect(position.x, position.y, size, size, size * 0.5);
        break;
      case 'rounded':
        this.drawRoundedRect(position.x, position.y, size, size, size * 0.25);
        break;
      default:
        this.ctx.fillRect(position.x, position.y, size, size);
    }
  }

  /**
   * Renders a corner dot
   * @param position Module position
   * @param size Module size
   * @param options Rendering options
   */
  private async renderCornerDot(position: { x: number; y: number }, size: number, options: Options): Promise<void> {
    if (!this.ctx) return;

    const cornerType = options.cornersDotOptions?.type || 'square';
    const color = options.cornersDotOptions?.color || options.dotsOptions?.color || '#000000';

    this.ctx.fillStyle = color;

    switch (cornerType) {
      case 'dot':
        this.drawCircle(position.x + size / 2, position.y + size / 2, size / 2);
        break;
      case 'square':
        this.ctx.fillRect(position.x, position.y, size, size);
        break;
      default:
        this.ctx.fillRect(position.x, position.y, size, size);
    }
  }

  /**
   * Renders a regular dot
   * @param position Module position
   * @param size Module size
   * @param row Module row
   * @param col Module column
   * @param qr QR Code instance
   * @param options Rendering options
   */
  private async renderDot(
    position: { x: number; y: number }, 
    size: number, 
    row: number, 
    col: number, 
    qr: QRCode, 
    options: Options
  ): Promise<void> {
    if (!this.ctx) return;

    const dotType = options.dotsOptions?.type || 'square';
    const color = options.dotsOptions?.color || '#000000';
    const neighbors = this.getNeighbors(row, col, qr);

    this.ctx.fillStyle = color;

    switch (dotType) {
      case 'dots':
        this.drawCircle(position.x + size / 2, position.y + size / 2, size / 2);
        break;
      case 'rounded':
        this.drawRoundedRect(position.x, position.y, size, size, size * 0.25);
        break;
      case 'extra-rounded':
        this.drawRoundedRect(position.x, position.y, size, size, size * 0.5);
        break;
      case 'classy':
        this.drawClassyDot(position, size, neighbors);
        break;
      case 'classy-rounded':
        this.drawClassyRoundedDot(position, size, neighbors);
        break;
      case 'square':
      default:
        this.ctx.fillRect(position.x, position.y, size, size);
    }
  }

  /**
   * Draws a circle
   * @param x Center X
   * @param y Center Y
   * @param radius Circle radius
   */
  private drawCircle(x: number, y: number, radius: number): void {
    if (!this.ctx) return;

    this.ctx.beginPath();
    this.ctx.arc(x, y, radius, 0, 2 * Math.PI);
    this.ctx.fill();
  }

  /**
   * Draws a rounded rectangle
   * @param x Rectangle X
   * @param y Rectangle Y
   * @param width Rectangle width
   * @param height Rectangle height
   * @param radius Corner radius
   */
  private drawRoundedRect(x: number, y: number, width: number, height: number, radius: number): void {
    if (!this.ctx) return;

    this.ctx.beginPath();
    this.ctx.moveTo(x + radius, y);
    this.ctx.lineTo(x + width - radius, y);
    this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    this.ctx.lineTo(x + width, y + height - radius);
    this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    this.ctx.lineTo(x + radius, y + height);
    this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    this.ctx.lineTo(x, y + radius);
    this.ctx.quadraticCurveTo(x, y, x + radius, y);
    this.ctx.closePath();
    this.ctx.fill();
  }

  /**
   * Draws a classy dot (connected to neighbors)
   * @param position Module position
   * @param size Module size
   * @param neighbors Neighbor information
   */
  private drawClassyDot(position: { x: number; y: number }, size: number, neighbors: any): void {
    if (!this.ctx) return;

    // Implementation for classy dots that connect to neighbors
    // This is a simplified version - full implementation would be more complex
    this.ctx.fillRect(position.x, position.y, size, size);
  }

  /**
   * Draws a classy rounded dot
   * @param position Module position
   * @param size Module size
   * @param neighbors Neighbor information
   */
  private drawClassyRoundedDot(position: { x: number; y: number }, size: number, neighbors: any): void {
    if (!this.ctx) return;

    // Implementation for classy rounded dots
    // This is a simplified version - full implementation would be more complex
    this.drawRoundedRect(position.x, position.y, size, size, size * 0.25);
  }
}
