import { QRCode, Window } from "../../types";
import { RequiredOptions } from "../../core/QROptions";
import { SVGElementCreator } from "./SVGElementCreator";
import calculateImageSize from "../../tools/calculateImageSize";
import toDataUrl from "../../tools/toDataUrl";
import errorCorrectionPercents from "../../constants/errorCorrectionPercents";
import shapeTypes from "../../constants/shapeTypes";
import { Image } from "canvas";

/**
 * Draws images for SVG QR codes
 * Implements Single Responsibility Principle - only handles image drawing
 */
export class SVGImageDrawer {
  private elementCreator: SVGElementCreator;
  private window: Window;
  private image?: HTMLImageElement | Image;
  private imageUri?: string;

  constructor(elementCreator: SVGElementCreator, window: Window) {
    this.elementCreator = elementCreator;
    this.window = window;
  }

  /**
   * Calculates image size for QR code
   * @param qr QR Code instance
   * @param options Rendering options
   * @returns Image size information
   */
  async calculateImageSize(qr: QRCode, options: RequiredOptions): Promise<{
    hideXDots: number;
    hideYDots: number;
    width: number;
    height: number;
  }> {
    if (!options.image) {
      return { hideXDots: 0, hideYDots: 0, width: 0, height: 0 };
    }

    // Load image first
    await this.loadImage(options);
    
    if (!this.image) {
      return { hideXDots: 0, hideYDots: 0, width: 0, height: 0 };
    }

    const count = qr.getModuleCount();
    const minSize = Math.min(options.width, options.height) - options.margin * 2;
    const realQRSize = options.shape === shapeTypes.circle ? minSize / Math.sqrt(2) : minSize;
    const dotSize = this.roundSize(realQRSize / count, options);

    const { imageOptions, qrOptions } = options;
    const coverLevel = imageOptions.imageSize * errorCorrectionPercents[qrOptions.errorCorrectionLevel];
    const maxHiddenDots = Math.floor(coverLevel * count * count);

    return calculateImageSize({
      originalWidth: this.image.width,
      originalHeight: this.image.height,
      maxHiddenDots,
      maxHiddenAxisDots: count - 14,
      dotSize
    });
  }

  /**
   * Draws image on QR code
   * @param qr QR Code instance
   * @param options Rendering options
   * @param imageSize Image size information
   */
  async drawImage(
    qr: QRCode,
    options: RequiredOptions,
    imageSize: { width: number; height: number }
  ): Promise<void> {
    if (!options.image || !this.imageUri) {
      return;
    }

    const count = qr.getModuleCount();
    const minSize = Math.min(options.width, options.height) - options.margin * 2;
    const realQRSize = options.shape === shapeTypes.circle ? minSize / Math.sqrt(2) : minSize;
    const dotSize = this.roundSize(realQRSize / count, options);
    
    const xBeginning = this.roundSize((options.width - count * dotSize) / 2, options);
    const yBeginning = this.roundSize((options.height - count * dotSize) / 2, options);
    
    const dx = xBeginning + this.roundSize(options.imageOptions.margin + (count * dotSize - imageSize.width) / 2, options);
    const dy = yBeginning + this.roundSize(options.imageOptions.margin + (count * dotSize - imageSize.height) / 2, options);
    const dw = imageSize.width - options.imageOptions.margin * 2;
    const dh = imageSize.height - options.imageOptions.margin * 2;

    const image = this.elementCreator.createImage(dx, dy, dw, dh, this.imageUri);
    this.elementCreator.appendChild(image);
  }

  /**
   * Loads image from URL or data
   * @param options Rendering options
   * @returns Promise that resolves when image is loaded
   */
  private loadImage(options: RequiredOptions): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!options.image) {
        return reject("Image is not defined");
      }

      if (options.nodeCanvas?.loadImage) {
        // Node.js environment
        options.nodeCanvas
          .loadImage(options.image)
          .then((image: Image) => {
            this.image = image;
            if (options.imageOptions.saveAsBlob) {
              const canvas = options.nodeCanvas?.createCanvas(this.image.width, this.image.height);
              canvas?.getContext('2d')?.drawImage(image, 0, 0);
              this.imageUri = canvas?.toDataURL();
            } else {
              this.imageUri = options.image;
            }
            resolve();
          })
          .catch(reject);
      } else {
        // Browser environment
        const image = new this.window.Image();

        if (typeof options.imageOptions.crossOrigin === "string") {
          image.crossOrigin = options.imageOptions.crossOrigin;
        }

        this.image = image;
        image.onload = async () => {
          if (options.imageOptions.saveAsBlob) {
            this.imageUri = await toDataUrl(options.image || "", this.window);
          } else {
            this.imageUri = options.image;
          }
          resolve();
        };
        
        image.onerror = () => {
          reject(new Error(`Failed to load image: ${options.image}`));
        };
        
        image.src = options.image;
      }
    });
  }

  /**
   * Rounds size based on options
   * @param value Value to round
   * @param options Rendering options
   * @returns Rounded value
   */
  private roundSize(value: number, options: RequiredOptions): number {
    if (options.dotsOptions.roundSize) {
      return Math.floor(value);
    }
    return value;
  }

  /**
   * Gets loaded image
   * @returns Loaded image or undefined
   */
  getImage(): HTMLImageElement | Image | undefined {
    return this.image;
  }

  /**
   * Gets image URI
   * @returns Image URI or undefined
   */
  getImageUri(): string | undefined {
    return this.imageUri;
  }

  /**
   * Checks if image is loaded
   * @returns true if image is loaded
   */
  isImageLoaded(): boolean {
    return !!(this.image && this.imageUri);
  }

  /**
   * Clears loaded image data
   */
  clear(): void {
    this.image = undefined;
    this.imageUri = undefined;
  }

  /**
   * Validates image options
   * @param options Rendering options
   * @returns Validation result
   */
  validateImageOptions(options: RequiredOptions): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!options.image) {
      errors.push("Image URL is required");
    }

    if (options.imageOptions.imageSize < 0 || options.imageOptions.imageSize > 1) {
      errors.push("Image size must be between 0 and 1");
    }

    if (options.imageOptions.margin < 0) {
      errors.push("Image margin cannot be negative");
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
