import { <PERSON><PERSON>enderer } from "../base/BaseRenderer";
import { IRendererConfig } from "../../core/interfaces/IQRRenderer";
import { QRCode, Options } from "../../types";
import { RequiredOptions } from "../../core/QROptions";
import { SVGElementCreator } from "./SVGElementCreator";
import { SVGStyleApplier } from "./SVGStyleApplier";
import { SVGBackgroundDrawer } from "./SVGBackgroundDrawer";
import { SVGDotDrawer } from "./SVGDotDrawer";
import { SVGCornerDrawer } from "./SVGCornerDrawer";
import { SVGImageDrawer } from "./SVGImageDrawer";

/**
 * SVG-based QR Code renderer using composition of specialized components
 * Implements Single Responsibility Principle through delegation
 */
export class SVGRenderer extends BaseRenderer {
  private elementCreator!: SVGElementCreator;
  private styleApplier!: SVGStyleApplier;
  private backgroundDrawer!: SVGBackgroundDrawer;
  private dotDrawer!: SVGDotDrawer;
  private cornerDrawer!: SVGCornerDrawer;
  private imageDrawer!: SVGImageDrawer;

  constructor(config: IRendererConfig) {
    super(config);
  }

  /**
   * Checks if renderer supports the specified type
   * @param type Renderer type
   * @returns true if supported
   */
  supports(type: string): boolean {
    return type.toLowerCase() === 'svg';
  }

  /**
   * Creates the SVG element and initializes components
   */
  protected createElement(): void {
    // This will be called from the base class, but we need options
    // So we'll defer actual creation to setupElement
  }

  /**
   * Sets up SVG element and all components
   */
  protected setupElement(): void {
    // We need to cast to get access to the full options
    const options = this.context as any as RequiredOptions;
    
    if (!options) {
      throw new Error('Options not available for SVG setup');
    }

    // Create main components
    this.elementCreator = new SVGElementCreator(options, (global as any).window);
    this.styleApplier = new SVGStyleApplier((global as any).window, this.elementCreator);
    this.backgroundDrawer = new SVGBackgroundDrawer(this.elementCreator, this.styleApplier);
    this.dotDrawer = new SVGDotDrawer(this.elementCreator, this.styleApplier, (global as any).window);
    this.cornerDrawer = new SVGCornerDrawer(this.elementCreator, this.styleApplier, (global as any).window);
    this.imageDrawer = new SVGImageDrawer(this.elementCreator, (global as any).window);

    // Set the element
    this.element = this.elementCreator.getElement();
  }

  /**
   * Renders QR modules using specialized drawers
   * @param qr QR Code instance
   * @param options Rendering options
   */
  protected async renderModules(qr: QRCode, options: Options): Promise<void> {
    const requiredOptions = options as RequiredOptions;

    // Initialize components if not already done
    if (!this.elementCreator) {
      this.elementCreator = new SVGElementCreator(requiredOptions, (global as any).window);
      this.styleApplier = new SVGStyleApplier((global as any).window, this.elementCreator);
      this.backgroundDrawer = new SVGBackgroundDrawer(this.elementCreator, this.styleApplier);
      this.dotDrawer = new SVGDotDrawer(this.elementCreator, this.styleApplier, (global as any).window);
      this.cornerDrawer = new SVGCornerDrawer(this.elementCreator, this.styleApplier, (global as any).window);
      this.imageDrawer = new SVGImageDrawer(this.elementCreator, (global as any).window);
      
      this.element = this.elementCreator.getElement();
    }

    // Draw background
    this.backgroundDrawer.drawBackground(requiredOptions);

    // Calculate image size if needed
    let drawImageSize = {
      hideXDots: 0,
      hideYDots: 0,
      width: 0,
      height: 0
    };

    if (requiredOptions.image) {
      drawImageSize = await this.imageDrawer.calculateImageSize(qr, requiredOptions);
    }

    // Draw dots with filter for image area
    this.dotDrawer.drawDots(qr, requiredOptions, (row: number, col: number): boolean => {
      return this.shouldDrawDot(row, col, qr, requiredOptions, drawImageSize);
    });

    // Draw corners
    this.cornerDrawer.drawCorners(qr, requiredOptions);

    // Draw image if specified
    if (requiredOptions.image) {
      await this.imageDrawer.drawImage(qr, requiredOptions, drawImageSize);
    }
  }

  /**
   * Determines if a dot should be drawn based on position and image area
   * @param row Dot row
   * @param col Dot column
   * @param qr QR Code instance
   * @param options Rendering options
   * @param drawImageSize Image size information
   * @returns true if dot should be drawn
   */
  private shouldDrawDot(
    row: number, 
    col: number, 
    qr: QRCode, 
    options: RequiredOptions, 
    drawImageSize: any
  ): boolean {
    const count = qr.getModuleCount();

    // Check if in image area
    if (options.imageOptions.hideBackgroundDots) {
      if (
        row >= (count - drawImageSize.hideYDots) / 2 &&
        row < (count + drawImageSize.hideYDots) / 2 &&
        col >= (count - drawImageSize.hideXDots) / 2 &&
        col < (count + drawImageSize.hideXDots) / 2
      ) {
        return false;
      }
    }

    // Check if in corner square area
    if (this.isInCornerSquareArea(row, col, count)) {
      return false;
    }

    // Check if in corner dot area
    if (this.isInCornerDotArea(row, col, count)) {
      return false;
    }

    return true;
  }

  /**
   * Checks if position is in corner square area
   * @param row Row position
   * @param col Column position
   * @param count Module count
   * @returns true if in corner square area
   */
  private isInCornerSquareArea(row: number, col: number, count: number): boolean {
    const squareMask = [
      [1, 1, 1, 1, 1, 1, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 0, 0, 0, 0, 0, 1],
      [1, 1, 1, 1, 1, 1, 1]
    ];

    return !!(
      squareMask[row]?.[col] || 
      squareMask[row - count + 7]?.[col] || 
      squareMask[row]?.[col - count + 7]
    );
  }

  /**
   * Checks if position is in corner dot area
   * @param row Row position
   * @param col Column position
   * @param count Module count
   * @returns true if in corner dot area
   */
  private isInCornerDotArea(row: number, col: number, count: number): boolean {
    const dotMask = [
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 1, 1, 1, 0, 0],
      [0, 0, 1, 1, 1, 0, 0],
      [0, 0, 1, 1, 1, 0, 0],
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0]
    ];

    return !!(
      dotMask[row]?.[col] || 
      dotMask[row - count + 7]?.[col] || 
      dotMask[row]?.[col - count + 7]
    );
  }

  /**
   * Gets the SVG element
   * @returns SVG element
   */
  getSVGElement(): SVGElement {
    return this.elementCreator?.getElement() || this.element as SVGElement;
  }

  /**
   * Gets the element creator
   * @returns Element creator instance
   */
  getElementCreator(): SVGElementCreator {
    return this.elementCreator;
  }

  /**
   * Gets the style applier
   * @returns Style applier instance
   */
  getStyleApplier(): SVGStyleApplier {
    return this.styleApplier;
  }

  /**
   * Clears the renderer and all components
   */
  clear(): void {
    super.clear();
    
    // Clear component references
    this.elementCreator = undefined as any;
    this.styleApplier = undefined as any;
    this.backgroundDrawer = undefined as any;
    this.dotDrawer = undefined as any;
    this.cornerDrawer = undefined as any;
    this.imageDrawer = undefined as any;
  }
}
