import { QRCode, FilterFunction, Window } from "../../types";
import { RequiredOptions } from "../../core/QROptions";
import { SVGElementCreator } from "./SVGElementCreator";
import { SVGStyleApplier } from "./SVGStyleApplier";
import QRDot from "../../figures/dot/QRDot";
import shapeTypes from "../../constants/shapeTypes";

/**
 * Draws dots for SVG QR codes
 * Implements Single Responsibility Principle - only handles dot drawing
 */
export class SVGDotDrawer {
  private elementCreator: SVGElementCreator;
  private styleApplier: SVGStyleApplier;
  private window: Window;

  constructor(elementCreator: SVGElementCreator, styleApplier: SVGStyleApplier, window: Window) {
    this.elementCreator = elementCreator;
    this.styleApplier = styleApplier;
    this.window = window;
  }

  /**
   * Draws all dots for the QR code
   * @param qr QR Code instance
   * @param options Rendering options
   * @param filter Optional filter function
   */
  drawDots(qr: QRCode, options: RequiredOptions, filter?: FilterFunction): void {
    if (!qr) {
      throw new Error("QR code is not defined");
    }

    const count = qr.getModuleCount();

    if (count > options.width || count > options.height) {
      throw new Error("The canvas is too small.");
    }

    // Calculate dimensions
    const dimensions = this.calculateDimensions(options, count);
    
    // Create dots clip path
    const dotsClipPath = this.elementCreator.createDotsClipPath();

    // Create dot drawer
    const dot = new QRDot({
      svg: this.elementCreator.getElement(),
      type: options.dotsOptions.type,
      window: this.window
    });

    // Apply dots style
    this.applyDotsStyle(options, dimensions);

    // Draw regular dots
    this.drawRegularDots(qr, options, dimensions, dot, dotsClipPath, filter);

    // Draw additional dots for circular shape
    if (options.shape === shapeTypes.circle) {
      this.drawCircularShapeDots(qr, options, dimensions, dot, dotsClipPath);
    }
  }

  /**
   * Calculates drawing dimensions
   * @param options Rendering options
   * @param count Module count
   * @returns Drawing dimensions
   */
  private calculateDimensions(options: RequiredOptions, count: number): {
    minSize: number;
    realQRSize: number;
    dotSize: number;
    xBeginning: number;
    yBeginning: number;
  } {
    const minSize = Math.min(options.width, options.height) - options.margin * 2;
    const realQRSize = options.shape === shapeTypes.circle ? minSize / Math.sqrt(2) : minSize;
    const dotSize = this.roundSize(realQRSize / count, options);
    const xBeginning = this.roundSize((options.width - count * dotSize) / 2, options);
    const yBeginning = this.roundSize((options.height - count * dotSize) / 2, options);

    return {
      minSize,
      realQRSize,
      dotSize,
      xBeginning,
      yBeginning
    };
  }

  /**
   * Applies style to dots
   * @param options Rendering options
   * @param dimensions Drawing dimensions
   */
  private applyDotsStyle(options: RequiredOptions, dimensions: any): void {
    const instanceId = this.elementCreator.getInstanceId();

    this.styleApplier.createColor({
      options: options.dotsOptions.gradient,
      color: options.dotsOptions.color,
      additionalRotation: 0,
      x: dimensions.xBeginning,
      y: dimensions.yBeginning,
      width: dimensions.realQRSize,
      height: dimensions.realQRSize,
      name: `dots-${instanceId}`
    });
  }

  /**
   * Draws regular QR code dots
   * @param qr QR Code instance
   * @param options Rendering options
   * @param dimensions Drawing dimensions
   * @param dot Dot drawer instance
   * @param clipPath Clip path element
   * @param filter Optional filter function
   */
  private drawRegularDots(
    qr: QRCode,
    options: RequiredOptions,
    dimensions: any,
    dot: QRDot,
    clipPath: SVGElement,
    filter?: FilterFunction
  ): void {
    const count = qr.getModuleCount();

    for (let row = 0; row < count; row++) {
      for (let col = 0; col < count; col++) {
        if (filter && !filter(row, col)) {
          continue;
        }
        if (!qr.isDark(row, col)) {
          continue;
        }

        dot.draw(
          dimensions.xBeginning + col * dimensions.dotSize,
          dimensions.yBeginning + row * dimensions.dotSize,
          dimensions.dotSize,
          (xOffset: number, yOffset: number): boolean => {
            if (col + xOffset < 0 || row + yOffset < 0 || col + xOffset >= count || row + yOffset >= count) {
              return false;
            }
            if (filter && !filter(row + yOffset, col + xOffset)) {
              return false;
            }
            return qr.isDark(row + yOffset, col + xOffset);
          }
        );

        if (dot._element && clipPath) {
          clipPath.appendChild(dot._element);
        }
      }
    }
  }

  /**
   * Draws additional dots for circular shape
   * @param qr QR Code instance
   * @param options Rendering options
   * @param dimensions Drawing dimensions
   * @param dot Dot drawer instance
   * @param clipPath Clip path element
   */
  private drawCircularShapeDots(
    qr: QRCode,
    options: RequiredOptions,
    dimensions: any,
    dot: QRDot,
    clipPath: SVGElement
  ): void {
    const count = qr.getModuleCount();
    const additionalDots = this.roundSize((dimensions.minSize / dimensions.dotSize - count) / 2, options);
    const fakeCount = count + additionalDots * 2;
    const xFakeBeginning = dimensions.xBeginning - additionalDots * dimensions.dotSize;
    const yFakeBeginning = dimensions.yBeginning - additionalDots * dimensions.dotSize;
    const fakeMatrix: number[][] = [];
    const center = this.roundSize(fakeCount / 2, options);

    // Generate fake matrix for circular shape
    for (let row = 0; row < fakeCount; row++) {
      fakeMatrix[row] = [];
      for (let col = 0; col < fakeCount; col++) {
        if (
          row >= additionalDots - 1 &&
          row <= fakeCount - additionalDots &&
          col >= additionalDots - 1 &&
          col <= fakeCount - additionalDots
        ) {
          fakeMatrix[row][col] = 0;
          continue;
        }

        if (Math.sqrt((row - center) * (row - center) + (col - center) * (col - center)) > center) {
          fakeMatrix[row][col] = 0;
          continue;
        }

        // Get random dots from QR code to show outside of QR code
        fakeMatrix[row][col] = qr.isDark(
          col - 2 * additionalDots < 0 ? col : col >= count ? col - 2 * additionalDots : col - additionalDots,
          row - 2 * additionalDots < 0 ? row : row >= count ? row - 2 * additionalDots : row - additionalDots
        ) ? 1 : 0;
      }
    }

    // Draw fake dots
    for (let row = 0; row < fakeCount; row++) {
      for (let col = 0; col < fakeCount; col++) {
        if (!fakeMatrix[row][col]) continue;

        dot.draw(
          xFakeBeginning + col * dimensions.dotSize,
          yFakeBeginning + row * dimensions.dotSize,
          dimensions.dotSize,
          (xOffset: number, yOffset: number): boolean => {
            return !!fakeMatrix[row + yOffset]?.[col + xOffset];
          }
        );

        if (dot._element && clipPath) {
          clipPath.appendChild(dot._element);
        }
      }
    }
  }

  /**
   * Rounds size based on options
   * @param value Value to round
   * @param options Rendering options
   * @returns Rounded value
   */
  private roundSize(value: number, options: RequiredOptions): number {
    if (options.dotsOptions.roundSize) {
      return Math.floor(value);
    }
    return value;
  }

  /**
   * Gets dot drawing statistics
   * @param qr QR Code instance
   * @param filter Optional filter function
   * @returns Drawing statistics
   */
  getDrawingStats(qr: QRCode, filter?: FilterFunction): {
    totalModules: number;
    darkModules: number;
    drawnModules: number;
  } {
    const count = qr.getModuleCount();
    let darkModules = 0;
    let drawnModules = 0;

    for (let row = 0; row < count; row++) {
      for (let col = 0; col < count; col++) {
        if (qr.isDark(row, col)) {
          darkModules++;
          
          if (!filter || filter(row, col)) {
            drawnModules++;
          }
        }
      }
    }

    return {
      totalModules: count * count,
      darkModules,
      drawnModules
    };
  }
}
