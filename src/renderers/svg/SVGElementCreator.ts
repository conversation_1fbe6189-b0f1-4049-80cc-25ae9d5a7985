import { RequiredOptions } from "../../core/QROptions";
import { Window } from "../../types";

/**
 * Creates and manages SVG elements
 * Implements Single Responsibility Principle - only handles SVG element creation
 */
export class SVGElementCreator {
  private window: Window;
  private element!: SVGElement;
  private defs!: SVGElement;
  private backgroundClipPath?: SVGElement;
  private dotsClipPath?: SVGElement;
  private cornersSquareClipPath?: SVGElement;
  private cornersDotClipPath?: SVGElement;
  private instanceId: number;
  private static instanceCount = 0;

  constructor(options: RequiredOptions, window: Window) {
    this.window = window;
    this.instanceId = SVGElementCreator.instanceCount++;
    
    this.createElement(options);
    this.createDefs();
  }

  /**
   * Creates the main SVG element
   * @param options Rendering options
   */
  private createElement(options: RequiredOptions): void {
    this.element = this.window.document.createElementNS("http://www.w3.org/2000/svg", "svg");
    this.element.setAttribute("width", String(options.width));
    this.element.setAttribute("height", String(options.height));
    this.element.setAttribute("xmlns:xlink", "http://www.w3.org/1999/xlink");
    
    if (!options.dotsOptions.roundSize) {
      this.element.setAttribute("shape-rendering", "crispEdges");
    }
    
    this.element.setAttribute("viewBox", `0 0 ${options.width} ${options.height}`);
  }

  /**
   * Creates the defs element
   */
  private createDefs(): void {
    this.defs = this.window.document.createElementNS("http://www.w3.org/2000/svg", "defs");
    this.element.appendChild(this.defs);
  }

  /**
   * Gets the main SVG element
   * @returns SVG element
   */
  getElement(): SVGElement {
    return this.element;
  }

  /**
   * Gets the defs element
   * @returns Defs element
   */
  getDefs(): SVGElement {
    return this.defs;
  }

  /**
   * Gets the instance ID
   * @returns Instance ID
   */
  getInstanceId(): number {
    return this.instanceId;
  }

  /**
   * Creates a clip path element
   * @param name Clip path name
   * @returns Clip path element
   */
  createClipPath(name: string): SVGElement {
    const clipPath = this.window.document.createElementNS("http://www.w3.org/2000/svg", "clipPath");
    clipPath.setAttribute("id", `clip-path-${name}`);
    this.defs.appendChild(clipPath);
    return clipPath;
  }

  /**
   * Creates and sets background clip path
   * @returns Background clip path element
   */
  createBackgroundClipPath(): SVGElement {
    this.backgroundClipPath = this.createClipPath(`background-${this.instanceId}`);
    return this.backgroundClipPath;
  }

  /**
   * Creates and sets dots clip path
   * @returns Dots clip path element
   */
  createDotsClipPath(): SVGElement {
    this.dotsClipPath = this.createClipPath(`dots-${this.instanceId}`);
    return this.dotsClipPath;
  }

  /**
   * Creates and sets corners square clip path
   * @returns Corners square clip path element
   */
  createCornersSquareClipPath(): SVGElement {
    this.cornersSquareClipPath = this.createClipPath(`corners-square-${this.instanceId}`);
    return this.cornersSquareClipPath;
  }

  /**
   * Creates and sets corners dot clip path
   * @returns Corners dot clip path element
   */
  createCornersDotClipPath(): SVGElement {
    this.cornersDotClipPath = this.createClipPath(`corners-dot-${this.instanceId}`);
    return this.cornersDotClipPath;
  }

  /**
   * Gets background clip path
   * @returns Background clip path element
   */
  getBackgroundClipPath(): SVGElement | undefined {
    return this.backgroundClipPath;
  }

  /**
   * Gets dots clip path
   * @returns Dots clip path element
   */
  getDotsClipPath(): SVGElement | undefined {
    return this.dotsClipPath;
  }

  /**
   * Gets corners square clip path
   * @returns Corners square clip path element
   */
  getCornersSquareClipPath(): SVGElement | undefined {
    return this.cornersSquareClipPath;
  }

  /**
   * Gets corners dot clip path
   * @returns Corners dot clip path element
   */
  getCornersDotClipPath(): SVGElement | undefined {
    return this.cornersDotClipPath;
  }

  /**
   * Creates a rectangle element
   * @param x X position
   * @param y Y position
   * @param width Width
   * @param height Height
   * @returns Rectangle element
   */
  createRect(x: number, y: number, width: number, height: number): SVGRectElement {
    const rect = this.window.document.createElementNS("http://www.w3.org/2000/svg", "rect");
    rect.setAttribute("x", String(x));
    rect.setAttribute("y", String(y));
    rect.setAttribute("width", String(width));
    rect.setAttribute("height", String(height));
    return rect;
  }

  /**
   * Creates an image element
   * @param x X position
   * @param y Y position
   * @param width Width
   * @param height Height
   * @param href Image href
   * @returns Image element
   */
  createImage(x: number, y: number, width: number, height: number, href: string): SVGImageElement {
    const image = this.window.document.createElementNS("http://www.w3.org/2000/svg", "image");
    image.setAttribute("x", String(x));
    image.setAttribute("y", String(y));
    image.setAttribute("width", String(width));
    image.setAttribute("height", String(height));
    image.setAttributeNS("http://www.w3.org/1999/xlink", "href", href);
    return image;
  }

  /**
   * Creates a gradient element
   * @param id Gradient ID
   * @param type Gradient type ('linear' or 'radial')
   * @returns Gradient element
   */
  createGradient(id: string, type: 'linear' | 'radial'): SVGGradientElement {
    const gradientType = type === 'radial' ? 'radialGradient' : 'linearGradient';
    const gradient = this.window.document.createElementNS("http://www.w3.org/2000/svg", gradientType);
    gradient.setAttribute("id", id);
    return gradient;
  }

  /**
   * Creates a stop element for gradients
   * @param offset Stop offset
   * @param color Stop color
   * @returns Stop element
   */
  createStop(offset: number, color: string): SVGStopElement {
    const stop = this.window.document.createElementNS("http://www.w3.org/2000/svg", "stop");
    stop.setAttribute("offset", `${100 * offset}%`);
    stop.setAttribute("stop-color", color);
    return stop;
  }

  /**
   * Appends element to main SVG
   * @param element Element to append
   */
  appendChild(element: SVGElement): void {
    this.element.appendChild(element);
  }

  /**
   * Appends element to defs
   * @param element Element to append
   */
  appendToDefs(element: SVGElement): void {
    this.defs.appendChild(element);
  }
}
