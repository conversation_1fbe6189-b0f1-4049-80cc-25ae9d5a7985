import { Gradient, Window } from "../../types";
import { SVGElementCreator } from "./SVGElementCreator";
import gradientTypes from "../../constants/gradientTypes";

/**
 * Applies styles and gradients to SVG elements
 * Implements Single Responsibility Principle - only handles style application
 */
export class SVGStyleApplier {
  private window: Window;
  private elementCreator: SVGElementCreator;

  constructor(window: Window, elementCreator: SVGElementCreator) {
    this.window = window;
    this.elementCreator = elementCreator;
  }

  /**
   * Creates and applies color/gradient to an element
   * @param params Style parameters
   */
  createColor(params: {
    options?: Gradient;
    color?: string;
    additionalRotation: number;
    x: number;
    y: number;
    height: number;
    width: number;
    name: string;
  }): void {
    const { options, color, additionalRotation, x, y, height, width, name } = params;
    const size = width > height ? width : height;
    
    const rect = this.elementCreator.createRect(x, y, width, height);
    rect.setAttribute("clip-path", `url('#clip-path-${name}')`);

    if (options) {
      this.applyGradient(rect, options, additionalRotation, size, name);
    } else if (color) {
      rect.setAttribute("fill", color);
    }

    this.elementCreator.appendChild(rect);
  }

  /**
   * Applies gradient to an element
   * @param element Element to apply gradient to
   * @param gradient Gradient options
   * @param additionalRotation Additional rotation
   * @param size Element size
   * @param name Gradient name
   */
  private applyGradient(
    element: SVGElement,
    gradient: Gradient,
    additionalRotation: number,
    size: number,
    name: string
  ): void {
    const gradientId = `gradient-${name}`;
    const gradientElement = this.elementCreator.createGradient(gradientId, gradient.type);

    if (gradient.type === gradientTypes.radial) {
      this.setupRadialGradient(gradientElement as SVGRadialGradientElement, size);
    } else {
      this.setupLinearGradient(
        gradientElement as SVGLinearGradientElement, 
        gradient.rotation, 
        additionalRotation
      );
    }

    // Add color stops
    gradient.colorStops.forEach(({ offset, color }) => {
      const stop = this.elementCreator.createStop(offset, color);
      gradientElement.appendChild(stop);
    });

    element.setAttribute("fill", `url('#${gradientId}')`);
    this.elementCreator.appendToDefs(gradientElement);
  }

  /**
   * Sets up radial gradient attributes
   * @param gradient Radial gradient element
   * @param size Gradient size
   */
  private setupRadialGradient(gradient: SVGRadialGradientElement, size: number): void {
    gradient.setAttribute("cx", "50%");
    gradient.setAttribute("cy", "50%");
    gradient.setAttribute("r", "50%");
    gradient.setAttribute("fx", "50%");
    gradient.setAttribute("fy", "50%");
    gradient.setAttribute("gradientUnits", "objectBoundingBox");
  }

  /**
   * Sets up linear gradient attributes
   * @param gradient Linear gradient element
   * @param rotation Gradient rotation
   * @param additionalRotation Additional rotation
   */
  private setupLinearGradient(
    gradient: SVGLinearGradientElement, 
    rotation: number = 0, 
    additionalRotation: number = 0
  ): void {
    const totalRotation = ((rotation || 0) + additionalRotation) % (2 * Math.PI);
    const x1 = Math.round(50 + Math.cos(totalRotation) * 50);
    const y1 = Math.round(50 + Math.sin(totalRotation) * 50);
    const x2 = Math.round(50 + Math.cos(totalRotation + Math.PI) * 50);
    const y2 = Math.round(50 + Math.sin(totalRotation + Math.PI) * 50);

    gradient.setAttribute("x1", `${x1}%`);
    gradient.setAttribute("y1", `${y1}%`);
    gradient.setAttribute("x2", `${x2}%`);
    gradient.setAttribute("y2", `${y2}%`);
    gradient.setAttribute("gradientUnits", "objectBoundingBox");
  }

  /**
   * Applies background style
   * @param x X position
   * @param y Y position
   * @param width Width
   * @param height Height
   * @param options Background options
   */
  applyBackgroundStyle(
    x: number, 
    y: number, 
    width: number, 
    height: number, 
    options: { color?: string; gradient?: Gradient; round?: number }
  ): void {
    const instanceId = this.elementCreator.getInstanceId();
    
    this.createColor({
      options: options.gradient,
      color: options.color,
      additionalRotation: 0,
      x,
      y,
      width,
      height,
      name: `background-${instanceId}`
    });
  }

  /**
   * Applies dots style
   * @param options Dots options
   */
  applyDotsStyle(options: { color?: string; gradient?: Gradient }): void {
    
    if (options.gradient || options.color) {
      // Style will be applied when dots are drawn
      // This method can be extended for more complex dot styling
    }
  }

  /**
   * Applies corner square style
   * @param options Corner square options
   */
  applyCornerSquareStyle(options: { color?: string; gradient?: Gradient }): void {
    
    if (options.gradient || options.color) {
      // Style will be applied when corners are drawn
      // This method can be extended for more complex corner styling
    }
  }

  /**
   * Applies corner dot style
   * @param options Corner dot options
   */
  applyCornerDotStyle(options: { color?: string; gradient?: Gradient }): void {
    
    if (options.gradient || options.color) {
      // Style will be applied when corner dots are drawn
      // This method can be extended for more complex corner dot styling
    }
  }

  /**
   * Creates a styled element with color/gradient
   * @param elementType Element type to create
   * @param attributes Element attributes
   * @param style Style options
   * @returns Styled element
   */
  createStyledElement(
    elementType: string,
    attributes: Record<string, string>,
    style: { color?: string; gradient?: Gradient }
  ): SVGElement {
    const element = this.window.document.createElementNS("http://www.w3.org/2000/svg", elementType);
    
    // Set attributes
    Object.entries(attributes).forEach(([key, value]) => {
      element.setAttribute(key, value);
    });

    // Apply style
    if (style.gradient) {
      const gradientId = `gradient-${Date.now()}-${Math.random()}`;
      const gradientElement = this.elementCreator.createGradient(gradientId, style.gradient.type);
      
      // Setup gradient based on type
      if (style.gradient.type === gradientTypes.radial) {
        this.setupRadialGradient(gradientElement as SVGRadialGradientElement, 100);
      } else {
        this.setupLinearGradient(gradientElement as SVGLinearGradientElement, style.gradient.rotation);
      }

      // Add color stops
      style.gradient.colorStops.forEach(({ offset, color }) => {
        const stop = this.elementCreator.createStop(offset, color);
        gradientElement.appendChild(stop);
      });

      element.setAttribute("fill", `url('#${gradientId}')`);
      this.elementCreator.appendToDefs(gradientElement);
    } else if (style.color) {
      element.setAttribute("fill", style.color);
    }

    return element;
  }
}
