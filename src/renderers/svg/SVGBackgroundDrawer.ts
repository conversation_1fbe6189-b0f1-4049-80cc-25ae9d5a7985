import { RequiredOptions } from "../../core/QROptions";
import { SVGElementCreator } from "./SVGElementCreator";
import { SVGStyleApplier } from "./SVGStyleApplier";
import shapeTypes from "../../constants/shapeTypes";

/**
 * Draws background for SVG QR codes
 * Implements Single Responsibility Principle - only handles background drawing
 */
export class SVGBackgroundDrawer {
  private elementCreator: SVGElementCreator;
  private styleApplier: SVGStyleApplier;

  constructor(elementCreator: SVGElementCreator, styleApplier: SVGStyleApplier) {
    this.elementCreator = elementCreator;
    this.styleApplier = styleApplier;
  }

  /**
   * Draws the background
   * @param options Rendering options
   */
  drawBackground(options: RequiredOptions): void {
    const backgroundOptions = options.backgroundOptions;
    
    if (!backgroundOptions.color && !backgroundOptions.gradient) {
      return;
    }

    // Create background clip path
    const backgroundClipPath = this.elementCreator.createBackgroundClipPath();
    
    if (options.shape === shapeTypes.circle) {
      this.drawCircularBackground(options, backgroundClipPath);
    } else {
      this.drawRectangularBackground(options, backgroundClipPath);
    }

    // Apply background style
    this.styleApplier.applyBackgroundStyle(
      0,
      0,
      options.width,
      options.height,
      backgroundOptions
    );
  }

  /**
   * Draws circular background
   * @param options Rendering options
   * @param clipPath Clip path element
   */
  private drawCircularBackground(options: RequiredOptions, clipPath: SVGElement): void {
    const size = Math.min(options.width, options.height);
    const radius = (size - options.margin * 2) / 2;
    const centerX = options.width / 2;
    const centerY = options.height / 2;

    const circle = this.elementCreator.getElement().ownerDocument!.createElementNS(
      "http://www.w3.org/2000/svg", 
      "circle"
    );
    
    circle.setAttribute("cx", String(centerX));
    circle.setAttribute("cy", String(centerY));
    circle.setAttribute("r", String(radius));

    // Apply border radius if specified
    if (options.backgroundOptions.round) {
      // For circles, round doesn't apply, but we keep it for consistency
    }

    clipPath.appendChild(circle);
  }

  /**
   * Draws rectangular background
   * @param options Rendering options
   * @param clipPath Clip path element
   */
  private drawRectangularBackground(options: RequiredOptions, clipPath: SVGElement): void {
    const x = options.margin;
    const y = options.margin;
    const width = options.width - options.margin * 2;
    const height = options.height - options.margin * 2;

    if (options.backgroundOptions.round && options.backgroundOptions.round > 0) {
      this.drawRoundedRectBackground(x, y, width, height, options.backgroundOptions.round, clipPath);
    } else {
      this.drawSimpleRectBackground(x, y, width, height, clipPath);
    }
  }

  /**
   * Draws simple rectangular background
   * @param x X position
   * @param y Y position
   * @param width Width
   * @param height Height
   * @param clipPath Clip path element
   */
  private drawSimpleRectBackground(
    x: number, 
    y: number, 
    width: number, 
    height: number, 
    clipPath: SVGElement
  ): void {
    const rect = this.elementCreator.createRect(x, y, width, height);
    clipPath.appendChild(rect);
  }

  /**
   * Draws rounded rectangular background
   * @param x X position
   * @param y Y position
   * @param width Width
   * @param height Height
   * @param radius Border radius
   * @param clipPath Clip path element
   */
  private drawRoundedRectBackground(
    x: number, 
    y: number, 
    width: number, 
    height: number, 
    radius: number, 
    clipPath: SVGElement
  ): void {
    const rect = this.elementCreator.createRect(x, y, width, height);
    rect.setAttribute("rx", String(radius));
    rect.setAttribute("ry", String(radius));
    clipPath.appendChild(rect);
  }

  /**
   * Calculates background dimensions
   * @param options Rendering options
   * @returns Background dimensions
   */
  getBackgroundDimensions(options: RequiredOptions): {
    x: number;
    y: number;
    width: number;
    height: number;
    radius?: number;
  } {
    const x = options.margin;
    const y = options.margin;
    const width = options.width - options.margin * 2;
    const height = options.height - options.margin * 2;

    if (options.shape === shapeTypes.circle) {
      const size = Math.min(width, height);
      const radius = size / 2;
      const centerX = options.width / 2;
      const centerY = options.height / 2;

      return {
        x: centerX - radius,
        y: centerY - radius,
        width: size,
        height: size,
        radius
      };
    }

    return { x, y, width, height };
  }

  /**
   * Checks if background should be drawn
   * @param options Rendering options
   * @returns true if background should be drawn
   */
  shouldDrawBackground(options: RequiredOptions): boolean {
    return !!(options.backgroundOptions.color || options.backgroundOptions.gradient);
  }
}
