import { QRCode, Window, DotType } from "../../types";
import { RequiredOptions } from "../../core/QROptions";
import { SVGElementCreator } from "./SVGElementCreator";
import { SVGStyleApplier } from "./SVGStyleApplier";
import QRCornerSquare, { availableCornerSquareTypes } from "../../figures/cornerSquare/QRCornerSquare";
import QRCornerDot, { availableCornerDotTypes } from "../../figures/cornerDot/QRCornerDot";
import QRDot from "../../figures/dot/QRDot";
import shapeTypes from "../../constants/shapeTypes";

/**
 * Draws corners for SVG QR codes
 * Implements Single Responsibility Principle - only handles corner drawing
 */
export class SVGCornerDrawer {
  private elementCreator: SVGElementCreator;
  private styleApplier: SVGStyleApplier;
  private window: Window;

  // Corner masks
  private readonly squareMask = [
    [1, 1, 1, 1, 1, 1, 1],
    [1, 0, 0, 0, 0, 0, 1],
    [1, 0, 0, 0, 0, 0, 1],
    [1, 0, 0, 0, 0, 0, 1],
    [1, 0, 0, 0, 0, 0, 1],
    [1, 0, 0, 0, 0, 0, 1],
    [1, 1, 1, 1, 1, 1, 1]
  ];

  private readonly dotMask = [
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 1, 1, 1, 0, 0],
    [0, 0, 1, 1, 1, 0, 0],
    [0, 0, 1, 1, 1, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0]
  ];

  constructor(elementCreator: SVGElementCreator, styleApplier: SVGStyleApplier, window: Window) {
    this.elementCreator = elementCreator;
    this.styleApplier = styleApplier;
    this.window = window;
  }

  /**
   * Draws all corners for the QR code
   * @param qr QR Code instance
   * @param options Rendering options
   */
  drawCorners(qr: QRCode, options: RequiredOptions): void {
    if (!qr) {
      throw new Error("QR code is not defined");
    }

    const count = qr.getModuleCount();
    const dimensions = this.calculateDimensions(options, count);

    // Create clip paths
    const cornersSquareClipPath = this.elementCreator.createCornersSquareClipPath();
    const cornersDotClipPath = this.elementCreator.createCornersDotClipPath();

    // Apply corner styles
    this.applyCornerStyles(options, dimensions);

    // Draw three corners
    const cornerPositions = [
      [0, 0, 0],           // Top-left
      [1, 0, Math.PI / 2], // Top-right
      [0, 1, -Math.PI / 2] // Bottom-left
    ];

    cornerPositions.forEach(([column, row, rotation]) => {
      const x = dimensions.xBeginning + column * dimensions.dotSize * (count - 7);
      const y = dimensions.yBeginning + row * dimensions.dotSize * (count - 7);

      this.drawCornerSquare(x, y, dimensions, rotation, options, cornersSquareClipPath);
      this.drawCornerDot(x, y, dimensions, rotation, options, cornersDotClipPath);
    });
  }

  /**
   * Calculates drawing dimensions
   * @param options Rendering options
   * @param count Module count
   * @returns Drawing dimensions
   */
  private calculateDimensions(options: RequiredOptions, count: number) {
    const minSize = Math.min(options.width, options.height) - options.margin * 2;
    const realQRSize = options.shape === shapeTypes.circle ? minSize / Math.sqrt(2) : minSize;
    const dotSize = this.roundSize(realQRSize / count, options);
    const cornersSquareSize = dotSize * 7;
    const cornersDotSize = dotSize * 3;
    const xBeginning = this.roundSize((options.width - count * dotSize) / 2, options);
    const yBeginning = this.roundSize((options.height - count * dotSize) / 2, options);

    return {
      dotSize,
      cornersSquareSize,
      cornersDotSize,
      xBeginning,
      yBeginning
    };
  }

  /**
   * Applies corner styles
   * @param options Rendering options
   * @param dimensions Drawing dimensions
   */
  private applyCornerStyles(options: RequiredOptions, dimensions: any): void {
    const instanceId = this.elementCreator.getInstanceId();

    // Apply corner square style
    if (options.cornersSquareOptions?.gradient || options.cornersSquareOptions?.color) {
      this.styleApplier.createColor({
        options: options.cornersSquareOptions.gradient,
        color: options.cornersSquareOptions.color,
        additionalRotation: 0,
        x: 0,
        y: 0,
        width: options.width,
        height: options.height,
        name: `corners-square-${instanceId}`
      });
    }

    // Apply corner dot style
    if (options.cornersDotOptions?.gradient || options.cornersDotOptions?.color) {
      this.styleApplier.createColor({
        options: options.cornersDotOptions.gradient,
        color: options.cornersDotOptions.color,
        additionalRotation: 0,
        x: 0,
        y: 0,
        width: options.width,
        height: options.height,
        name: `corners-dot-${instanceId}`
      });
    }
  }

  /**
   * Draws a corner square
   * @param x X position
   * @param y Y position
   * @param dimensions Drawing dimensions
   * @param rotation Rotation angle
   * @param options Rendering options
   * @param clipPath Clip path element
   */
  private drawCornerSquare(
    x: number,
    y: number,
    dimensions: any,
    rotation: number,
    options: RequiredOptions,
    clipPath: SVGElement
  ): void {
    if (options.cornersSquareOptions?.type && availableCornerSquareTypes.includes(options.cornersSquareOptions.type)) {
      // Use specialized corner square
      const cornersSquare = new QRCornerSquare({
        svg: this.elementCreator.getElement(),
        type: options.cornersSquareOptions.type,
        window: this.window
      });

      cornersSquare.draw(x, y, dimensions.cornersSquareSize, rotation);

      if (cornersSquare._element && clipPath) {
        clipPath.appendChild(cornersSquare._element);
      }
    } else {
      // Use regular dots
      this.drawCornerSquareWithDots(x, y, dimensions, options, clipPath);
    }
  }

  /**
   * Draws corner square using regular dots
   * @param x X position
   * @param y Y position
   * @param dimensions Drawing dimensions
   * @param options Rendering options
   * @param clipPath Clip path element
   */
  private drawCornerSquareWithDots(
    x: number,
    y: number,
    dimensions: any,
    options: RequiredOptions,
    clipPath: SVGElement
  ): void {
    const dot = new QRDot({
      svg: this.elementCreator.getElement(),
      type: (options.cornersSquareOptions?.type as DotType) || options.dotsOptions.type,
      window: this.window
    });

    for (let row = 0; row < this.squareMask.length; row++) {
      for (let col = 0; col < this.squareMask[row].length; col++) {
        if (!this.squareMask[row]?.[col]) {
          continue;
        }

        dot.draw(
          x + col * dimensions.dotSize,
          y + row * dimensions.dotSize,
          dimensions.dotSize,
          (xOffset: number, yOffset: number): boolean => !!this.squareMask[row + yOffset]?.[col + xOffset]
        );

        if (dot._element && clipPath) {
          clipPath.appendChild(dot._element);
        }
      }
    }
  }

  /**
   * Draws a corner dot
   * @param x X position
   * @param y Y position
   * @param dimensions Drawing dimensions
   * @param rotation Rotation angle
   * @param options Rendering options
   * @param clipPath Clip path element
   */
  private drawCornerDot(
    x: number,
    y: number,
    dimensions: any,
    rotation: number,
    options: RequiredOptions,
    clipPath: SVGElement
  ): void {
    if (options.cornersDotOptions?.type && availableCornerDotTypes.includes(options.cornersDotOptions.type)) {
      // Use specialized corner dot
      const cornersDot = new QRCornerDot({
        svg: this.elementCreator.getElement(),
        type: options.cornersDotOptions.type,
        window: this.window
      });

      cornersDot.draw(x + dimensions.dotSize * 2, y + dimensions.dotSize * 2, dimensions.cornersDotSize, rotation);

      if (cornersDot._element && clipPath) {
        clipPath.appendChild(cornersDot._element);
      }
    } else {
      // Use regular dots
      this.drawCornerDotWithDots(x, y, dimensions, options, clipPath);
    }
  }

  /**
   * Draws corner dot using regular dots
   * @param x X position
   * @param y Y position
   * @param dimensions Drawing dimensions
   * @param options Rendering options
   * @param clipPath Clip path element
   */
  private drawCornerDotWithDots(
    x: number,
    y: number,
    dimensions: any,
    options: RequiredOptions,
    clipPath: SVGElement
  ): void {
    const dot = new QRDot({
      svg: this.elementCreator.getElement(),
      type: (options.cornersDotOptions?.type as DotType) || options.dotsOptions.type,
      window: this.window
    });

    for (let row = 0; row < this.dotMask.length; row++) {
      for (let col = 0; col < this.dotMask[row].length; col++) {
        if (!this.dotMask[row]?.[col]) {
          continue;
        }

        dot.draw(
          x + col * dimensions.dotSize,
          y + row * dimensions.dotSize,
          dimensions.dotSize,
          (xOffset: number, yOffset: number): boolean => !!this.dotMask[row + yOffset]?.[col + xOffset]
        );

        if (dot._element && clipPath) {
          clipPath.appendChild(dot._element);
        }
      }
    }
  }

  /**
   * Rounds size based on options
   * @param value Value to round
   * @param options Rendering options
   * @returns Rounded value
   */
  private roundSize(value: number, options: RequiredOptions): number {
    if (options.dotsOptions.roundSize) {
      return Math.floor(value);
    }
    return value;
  }
}
