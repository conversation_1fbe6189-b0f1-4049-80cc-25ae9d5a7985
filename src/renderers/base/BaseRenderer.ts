import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er<PERSON>onfig, IRenderContext } from "../../core/interfaces/IQRRenderer";
import { QRCode, Options } from "../../types";

/**
 * Base renderer class implementing common functionality
 * Implements Template Method Pattern
 */
export abstract class BaseRenderer implements IQRRenderer {
  protected config: IRendererConfig;
  protected context?: IRenderContext;
  protected element?: HTMLElement | SVGElement | HTMLCanvasElement;

  constructor(config: IRendererConfig) {
    this.config = config;
  }

  /**
   * Renders the QR Code (Template Method)
   * @param qr QR Code instance
   * @param options Rendering options
   */
  async render(qr: QRCode, options: Options): Promise<void> {
    // Setup rendering context
    this.setupContext(qr, options);
    
    // Create element
    this.createElement();
    
    // Setup element properties
    this.setupElement();
    
    // Render QR modules
    await this.renderModules(qr, options);
    
    // Apply post-processing
    await this.postProcess(options);
  }

  /**
   * Gets the rendered element
   * @returns Rendered element
   */
  getElement(): HTMLElement | SVGElement | HTMLCanvasElement | null {
    return this.element || null;
  }

  /**
   * Clears the renderer
   */
  clear(): void {
    this.element = undefined;
    this.context = undefined;
  }

  /**
   * Checks if renderer supports the specified type
   * @param type Renderer type
   * @returns true if supported
   */
  abstract supports(type: string): boolean;

  /**
   * Sets up the rendering context
   * @param qr QR Code instance
   * @param options Rendering options
   */
  protected setupContext(qr: QRCode, options: Options): void {
    const moduleCount = qr.getModuleCount();
    const moduleSize = Math.floor((this.config.width - this.config.margin * 2) / moduleCount);
    
    this.context = {
      width: this.config.width,
      height: this.config.height,
      moduleCount,
      moduleSize,
      margin: this.config.margin
    };
  }

  /**
   * Creates the rendering element (abstract)
   */
  protected abstract createElement(): void;

  /**
   * Sets up element properties (abstract)
   */
  protected abstract setupElement(): void;

  /**
   * Renders QR modules (abstract)
   * @param qr QR Code instance
   * @param options Rendering options
   */
  protected abstract renderModules(qr: QRCode, options: Options): Promise<void>;

  /**
   * Applies post-processing effects
   * @param options Rendering options
   */
  protected async postProcess(options: Options): Promise<void> {
    // Default implementation - can be overridden
    if (options.imageOptions?.hideBackgroundDots && options.image) {
      await this.addCenterImage(options.image, options.imageOptions);
    }
  }

  /**
   * Adds center image to QR code
   * @param imageUrl Image URL
   * @param imageOptions Image options
   */
  protected async addCenterImage(imageUrl: string, imageOptions: any): Promise<void> {
    // Default implementation - should be overridden by specific renderers
    console.warn('addCenterImage not implemented for this renderer');
  }

  /**
   * Calculates module position
   * @param row Module row
   * @param col Module column
   * @returns Position coordinates
   */
  protected getModulePosition(row: number, col: number): { x: number; y: number } {
    if (!this.context) {
      throw new Error('Rendering context not initialized');
    }

    return {
      x: this.context.margin + col * this.context.moduleSize,
      y: this.context.margin + row * this.context.moduleSize
    };
  }

  /**
   * Checks if a module is a corner square
   * @param row Module row
   * @param col Module column
   * @returns true if corner square
   */
  protected isCornerSquare(row: number, col: number): boolean {
    if (!this.context) return false;
    
    const moduleCount = this.context.moduleCount;
    
    return (
      (row < 9 && col < 9) ||
      (row < 9 && col >= moduleCount - 8) ||
      (row >= moduleCount - 8 && col < 9)
    );
  }

  /**
   * Checks if a module is a corner dot
   * @param row Module row
   * @param col Module column
   * @returns true if corner dot
   */
  protected isCornerDot(row: number, col: number): boolean {
    if (!this.context) return false;
    
    const moduleCount = this.context.moduleCount;
    
    return (
      (row >= 2 && row <= 6 && col >= 2 && col <= 6) ||
      (row >= 2 && row <= 6 && col >= moduleCount - 7 && col <= moduleCount - 3) ||
      (row >= moduleCount - 7 && row <= moduleCount - 3 && col >= 2 && col <= 6)
    );
  }

  /**
   * Gets neighboring modules
   * @param row Module row
   * @param col Module column
   * @param qr QR Code instance
   * @returns Neighbor information
   */
  protected getNeighbors(row: number, col: number, qr: QRCode): {
    top: boolean;
    right: boolean;
    bottom: boolean;
    left: boolean;
  } {
    return {
      top: row > 0 ? qr.isDark(row - 1, col) : false,
      right: col < qr.getModuleCount() - 1 ? qr.isDark(row, col + 1) : false,
      bottom: row < qr.getModuleCount() - 1 ? qr.isDark(row + 1, col) : false,
      left: col > 0 ? qr.isDark(row, col - 1) : false
    };
  }
}
