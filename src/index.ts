// Legacy exports for backward compatibility
import QRCodeStyling from "./core/QRCodeStyling";
import dotTypes from "./constants/dotTypes";
import cornerDotTypes from "./constants/cornerDotTypes";
import cornerSquareTypes from "./constants/cornerSquareTypes";
import errorCorrectionLevels from "./constants/errorCorrectionLevels";
import errorCorrectionPercents from "./constants/errorCorrectionPercents";
import modes from "./constants/modes";
import qrTypes from "./constants/qrTypes";
import drawTypes from "./constants/drawTypes";
import shapeTypes from "./constants/shapeTypes";
import gradientTypes from "./constants/gradientTypes";

// New frontend exports (browser-compatible)
import { PixQRApp } from "./frontend/PixQRApp";
import { SimpleUnifiedQRCode } from "./frontend/SimpleUnifiedQRCode";

// Export all types
export * from "./types";

// Export legacy constants for backward compatibility
export {
  dotTypes,
  cornerDotTypes,
  cornerSquareTypes,
  errorCorrectionLevels,
  errorCorrectionPercents,
  modes,
  qrTypes,
  drawTypes,
  shapeTypes,
  gradientTypes
};

// Export legacy QRCodeStyling for backward compatibility
export { QRCodeStyling };

// Export new frontend classes (browser-compatible)
export { PixQRApp, SimpleUnifiedQRCode };

// Default export is now the SimpleUnifiedQRCode for browser compatibility
export default SimpleUnifiedQRCode;
