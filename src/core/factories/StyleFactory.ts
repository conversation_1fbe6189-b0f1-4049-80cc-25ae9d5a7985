import { IStyleEngine, IStyleProcessor } from "../interfaces/IStyleEngine";
import { StandardStyleEngine } from "../../styles/processors/StandardStyleEngine";
import { AdvancedStyleEngine } from "../../styles/processors/AdvancedStyleEngine";
import { DotStyleProcessor } from "../../styles/dots/DotStyleProcessor";
// import { CornerStyleProcessor } from "../../styles/corners/CornerStyleProcessor";
// import { GradientProcessor } from "../../styles/gradients/GradientProcessor";

/**
 * Factory for creating style engines and processors
 * Implements Factory Pattern for style system
 */
export class StyleFactory {
  private static engines = new Map<string, new () => IStyleEngine>();
  private static processors = new Map<string, new () => IStyleProcessor>();

  static {
    // Register default style engines
    this.registerEngine('standard', StandardStyleEngine);
    this.registerEngine('advanced', AdvancedStyleEngine);

    // Register default style processors
    this.registerProcessor('dot', DotStyleProcessor);
    // this.registerProcessor('corner', CornerStyleProcessor);
    // this.registerProcessor('gradient', GradientProcessor);
  }

  /**
   * Creates a style engine instance
   * @param type Engine type
   * @returns Style engine instance
   */
  static createEngine(type: string): IStyleEngine {
    const EngineClass = this.engines.get(type.toLowerCase());
    
    if (!EngineClass) {
      throw new Error(`Style engine type '${type}' is not supported. Available types: ${Array.from(this.engines.keys()).join(', ')}`);
    }

    return new EngineClass();
  }

  /**
   * Creates a style processor instance
   * @param type Processor type
   * @returns Style processor instance
   */
  static createProcessor(type: string): IStyleProcessor {
    const ProcessorClass = this.processors.get(type.toLowerCase());
    
    if (!ProcessorClass) {
      throw new Error(`Style processor type '${type}' is not supported. Available types: ${Array.from(this.processors.keys()).join(', ')}`);
    }

    return new ProcessorClass();
  }

  /**
   * Registers a new style engine type
   * @param type Engine type name
   * @param engineClass Engine class constructor
   */
  static registerEngine(type: string, engineClass: new () => IStyleEngine): void {
    this.engines.set(type.toLowerCase(), engineClass);
  }

  /**
   * Registers a new style processor type
   * @param type Processor type name
   * @param processorClass Processor class constructor
   */
  static registerProcessor(type: string, processorClass: new () => IStyleProcessor): void {
    this.processors.set(type.toLowerCase(), processorClass);
  }

  /**
   * Gets all available engine types
   * @returns Array of engine type names
   */
  static getAvailableEngineTypes(): string[] {
    return Array.from(this.engines.keys());
  }

  /**
   * Gets all available processor types
   * @returns Array of processor type names
   */
  static getAvailableProcessorTypes(): string[] {
    return Array.from(this.processors.keys());
  }

  /**
   * Checks if an engine type is supported
   * @param type Engine type name
   * @returns true if supported
   */
  static isEngineSupported(type: string): boolean {
    return this.engines.has(type.toLowerCase());
  }

  /**
   * Checks if a processor type is supported
   * @param type Processor type name
   * @returns true if supported
   */
  static isProcessorSupported(type: string): boolean {
    return this.processors.has(type.toLowerCase());
  }
}
