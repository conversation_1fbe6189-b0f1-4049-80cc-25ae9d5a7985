import { IQRGenerator } from "../interfaces/IQRGenerator";
import { StandardQRGenerator } from "../../pix/generators/StandardQRGenerator";
import { PixQRGenerator } from "../../pix/generators/PixQRGenerator";

/**
 * Factory for creating QR generators
 * Implements Factory Pattern for QR generation system
 */
export class GeneratorFactory {
  private static generators = new Map<string, new () => IQRGenerator>();

  static {
    // Register default generators
    this.register('standard', StandardQRGenerator);
    this.register('pix', PixQRGenerator);
  }

  /**
   * Creates a QR generator instance
   * @param type Generator type
   * @returns QR generator instance
   */
  static create(type: string): IQRGenerator {
    const GeneratorClass = this.generators.get(type.toLowerCase());
    
    if (!GeneratorClass) {
      throw new Error(`QR generator type '${type}' is not supported. Available types: ${Array.from(this.generators.keys()).join(', ')}`);
    }

    return new GeneratorClass();
  }

  /**
   * Registers a new QR generator type
   * @param type Generator type name
   * @param generatorClass Generator class constructor
   */
  static register(type: string, generatorClass: new () => IQRGenerator): void {
    this.generators.set(type.toLowerCase(), generatorClass);
  }

  /**
   * Unregisters a QR generator type
   * @param type Generator type name
   */
  static unregister(type: string): void {
    this.generators.delete(type.toLowerCase());
  }

  /**
   * Gets all available generator types
   * @returns Array of generator type names
   */
  static getAvailableTypes(): string[] {
    return Array.from(this.generators.keys());
  }

  /**
   * Checks if a generator type is supported
   * @param type Generator type name
   * @returns true if supported
   */
  static isSupported(type: string): boolean {
    return this.generators.has(type.toLowerCase());
  }
}
