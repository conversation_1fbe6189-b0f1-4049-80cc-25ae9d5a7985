import { IImageProcessor } from "../interfaces/IImageProcessor";
import { StandardImageProcessor } from "../../image/processors/StandardImageProcessor";
import { AdvancedImageProcessor } from "../../image/processors/AdvancedImageProcessor";

/**
 * Factory for creating image processors
 * Implements Factory Pattern for image processing system
 */
export class ImageProcessorFactory {
  private static processors = new Map<string, new () => IImageProcessor>();

  static {
    // Register default image processors
    this.register('standard', StandardImageProcessor);
    this.register('advanced', AdvancedImageProcessor);
  }

  /**
   * Creates an image processor instance
   * @param type Processor type
   * @returns Image processor instance
   */
  static create(type: string): IImageProcessor {
    const ProcessorClass = this.processors.get(type.toLowerCase());
    
    if (!ProcessorClass) {
      throw new Error(`Image processor type '${type}' is not supported. Available types: ${Array.from(this.processors.keys()).join(', ')}`);
    }

    return new ProcessorClass();
  }

  /**
   * Registers a new image processor type
   * @param type Processor type name
   * @param processorClass Processor class constructor
   */
  static register(type: string, processorClass: new () => IImageProcessor): void {
    this.processors.set(type.toLowerCase(), processorClass);
  }

  /**
   * Unregisters an image processor type
   * @param type Processor type name
   */
  static unregister(type: string): void {
    this.processors.delete(type.toLowerCase());
  }

  /**
   * Gets all available processor types
   * @returns Array of processor type names
   */
  static getAvailableTypes(): string[] {
    return Array.from(this.processors.keys());
  }

  /**
   * Checks if a processor type is supported
   * @param type Processor type name
   * @returns true if supported
   */
  static isSupported(type: string): boolean {
    return this.processors.has(type.toLowerCase());
  }
}
