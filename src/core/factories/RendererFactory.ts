import { IQR<PERSON>enderer, IRendererConfig } from "../interfaces/IQRRenderer";
import { CanvasRenderer } from "../../renderers/canvas/CanvasRenderer";
import { SVGRenderer } from "../../renderers/svg/SVGRenderer";

/**
 * Factory for creating QR renderers
 * Implements Factory Pattern and Open/Closed Principle
 */
export class RendererFactory {
  private static renderers = new Map<string, new (config: IRendererConfig) => IQRRenderer>();

  static {
    // Register default renderers
    this.register('canvas', CanvasRenderer);
    this.register('svg', SVGRenderer);
  }

  /**
   * Creates a renderer instance
   * @param type Renderer type
   * @param config Renderer configuration
   * @returns Renderer instance
   */
  static create(type: string, config: IRendererConfig): IQRRenderer {
    const RendererClass = this.renderers.get(type.toLowerCase());
    
    if (!RendererClass) {
      throw new Error(`Renderer type '${type}' is not supported. Available types: ${Array.from(this.renderers.keys()).join(', ')}`);
    }

    return new RendererClass(config);
  }

  /**
   * Registers a new renderer type
   * @param type Renderer type name
   * @param rendererClass Renderer class constructor
   */
  static register(type: string, rendererClass: new (config: IRendererConfig) => IQRRenderer): void {
    this.renderers.set(type.toLowerCase(), rendererClass);
  }

  /**
   * Unregisters a renderer type
   * @param type Renderer type name
   */
  static unregister(type: string): void {
    this.renderers.delete(type.toLowerCase());
  }

  /**
   * Gets all available renderer types
   * @returns Array of renderer type names
   */
  static getAvailableTypes(): string[] {
    return Array.from(this.renderers.keys());
  }

  /**
   * Checks if a renderer type is supported
   * @param type Renderer type name
   * @returns true if supported
   */
  static isSupported(type: string): boolean {
    return this.renderers.has(type.toLowerCase());
  }
}
