// Core interfaces for the QR Code Styling library
// Implements SOLID principles through well-defined abstractions

export * from './IQRRenderer';
export * from './IStyleEngine';
export * from './IImageProcessor';
export * from './IQRGenerator';

/**
 * Main factory interface for creating instances
 * Implements Factory Pattern and Dependency Inversion Principle
 */
export interface IQRCodeFactory {
  /**
   * Creates a QR renderer instance
   * @param type Type of renderer ('canvas' | 'svg')
   * @returns Renderer instance
   */
  createRenderer(type: string): import('./IQRRenderer').IQRRenderer;

  /**
   * Creates a style engine instance
   * @param type Type of style engine
   * @returns Style engine instance
   */
  createStyleEngine(type: string): import('./IStyleEngine').IStyleEngine;

  /**
   * Creates an image processor instance
   * @param type Type of image processor
   * @returns Image processor instance
   */
  createImageProcessor(type: string): import('./IImageProcessor').IImageProcessor;

  /**
   * Creates a QR generator instance
   * @param type Type of generator ('standard' | 'pix')
   * @returns Generator instance
   */
  createGenerator(type: string): import('./IQRGenerator').IQRGenerator;
}

/**
 * Configuration interface for the main QR Code manager
 */
export interface IQRCodeConfig {
  rendererType: 'canvas' | 'svg';
  styleEngineType: string;
  imageProcessorType: string;
  generatorType: 'standard' | 'pix';
  defaultOptions: any;
}

/**
 * Main manager interface that orchestrates all components
 * Implements Facade Pattern
 */
export interface IQRCodeManager {
  /**
   * Initializes the manager with configuration
   * @param config Configuration object
   */
  initialize(config: IQRCodeConfig): void;

  /**
   * Generates and renders a QR code
   * @param data Data for the QR code
   * @param options Rendering options
   * @returns Promise that resolves when complete
   */
  generateQRCode(data: string, options: any): Promise<any>;

  /**
   * Updates existing QR code with new options
   * @param options New options
   * @returns Promise that resolves when updated
   */
  updateQRCode(options: any): Promise<any>;

  /**
   * Exports QR code in specified format
   * @param format Export format
   * @param options Export options
   * @returns Promise with exported data
   */
  exportQRCode(format: string, options?: any): Promise<any>;

  /**
   * Cleans up resources
   */
  dispose(): void;
}
