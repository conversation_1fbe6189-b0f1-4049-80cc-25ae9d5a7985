import { QRCode, Options } from "../../types";

/**
 * Interface para renderizadores de QR Code
 * Implementa o princípio da Inversão de Dependência (DIP)
 */
export interface IQRRenderer {
  /**
   * Renderiza o QR Code
   * @param qr Instância do QR Code
   * @param options Opções de renderização
   * @returns Promise que resolve quando a renderização estiver completa
   */
  render(qr: QRCode, options: Options): Promise<void>;

  /**
   * Obtém o elemento renderizado
   * @returns Elemento renderizado (Canvas, SVG, etc.)
   */
  getElement(): HTMLElement | SVGElement | HTMLCanvasElement | null;

  /**
   * Limpa o renderizador
   */
  clear(): void;

  /**
   * Verifica se o renderizador suporta o tipo especificado
   * @param type Tipo de renderização
   * @returns true se suportado
   */
  supports(type: string): boolean;
}

/**
 * Interface para configuração de renderizadores
 */
export interface IRendererConfig {
  width: number;
  height: number;
  margin: number;
  type: 'canvas' | 'svg';
}

/**
 * Interface para contexto de renderização
 */
export interface IRenderContext {
  width: number;
  height: number;
  moduleCount: number;
  moduleSize: number;
  margin: number;
}
