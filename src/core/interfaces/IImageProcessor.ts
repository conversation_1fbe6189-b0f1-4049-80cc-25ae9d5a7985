/**
 * Interface para processadores de imagem
 * Implementa o princípio da Segregação de Interface (ISP)
 */
export interface IImageProcessor {
  /**
   * Processa uma imagem
   * @param imageData Dados da imagem
   * @param options Opções de processamento
   * @returns Promise com a imagem processada
   */
  process(imageData: Buffer | string, options: IImageProcessOptions): Promise<Buffer>;

  /**
   * Verifica se suporta o formato
   * @param format Formato da imagem
   * @returns true se suportado
   */
  supportsFormat(format: string): boolean;
}

/**
 * Interface para composição de imagens
 */
export interface IImageComposer {
  /**
   * Compõe múltiplas imagens
   * @param layers Camadas de imagem
   * @returns Promise com a imagem composta
   */
  compose(layers: IImageLayer[]): Promise<Buffer>;

  /**
   * Adiciona uma camada
   * @param layer Camada a ser adicionada
   */
  addLayer(layer: IImageLayer): void;

  /**
   * Remove uma camada
   * @param index Índice da camada
   */
  removeLayer(index: number): void;
}

/**
 * Interface para exportadores de imagem
 */
export interface IImageExporter {
  /**
   * Exporta imagem no formato especificado
   * @param imageData Dados da imagem
   * @param format Formato de saída
   * @param options Opções de exportação
   * @returns Promise com a imagem exportada
   */
  export(imageData: Buffer, format: string, options?: IExportOptions): Promise<Buffer>;

  /**
   * Converte para base64
   * @param imageData Dados da imagem
   * @param mimeType Tipo MIME
   * @returns String base64
   */
  toBase64(imageData: Buffer, mimeType: string): string;

  /**
   * Salva imagem em arquivo
   * @param imageData Dados da imagem
   * @param filePath Caminho do arquivo
   * @returns Promise que resolve quando salvo
   */
  saveToFile(imageData: Buffer, filePath: string): Promise<void>;
}

/**
 * Opções de processamento de imagem
 */
export interface IImageProcessOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: string;
  resize?: 'fit' | 'fill' | 'cover' | 'contain';
  background?: string;
}

/**
 * Camada de imagem para composição
 */
export interface IImageLayer {
  data: Buffer;
  x: number;
  y: number;
  width?: number;
  height?: number;
  opacity?: number;
  blendMode?: string;
}

/**
 * Opções de exportação
 */
export interface IExportOptions {
  quality?: number;
  compression?: number;
  progressive?: boolean;
  metadata?: Record<string, any>;
}
