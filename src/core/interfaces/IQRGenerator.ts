import { QRCode } from "../../types";

/**
 * Interface para geradores de QR Code
 * Implementa o princípio Abe<PERSON>/Fe<PERSON>do (OCP)
 */
export interface IQRGenerator {
  /**
   * Gera um QR Code
   * @param data Dados para o QR Code
   * @param options Opções de geração
   * @returns Instância do QR Code
   */
  generate(data: string, options: IQRGenerationOptions): QRCode;

  /**
   * Valida os dados de entrada
   * @param data Dados a serem validados
   * @returns true se válidos
   */
  validateData(data: string): boolean;

  /**
   * Obtém o modo apropriado para os dados
   * @param data Dados de entrada
   * @returns Modo recomendado
   */
  getOptimalMode(data: string): string;
}

/**
 * Interface para geradores PIX específicos
 */
export interface IPixGenerator extends IQRGenerator {
  /**
   * Gera BR-Code para PIX
   * @param pixData Dados PIX
   * @returns String BR-Code
   */
  generateBRCode(pixData: IPixData): string;

  /**
   * Valida chave PIX
   * @param key Chave PIX
   * @param type Tipo da chave
   * @returns true se válida
   */
  validatePixKey(key: string, type: PixKeyType): boolean;

  /**
   * Calcula CRC para BR-Code
   * @param data Dados para cálculo
   * @returns CRC calculado
   */
  calculateCRC(data: string): string;
}

/**
 * Interface para configuração de QR Code
 */
export interface IQRConfig {
  typeNumber: number;
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H';
  mode?: 'Numeric' | 'Alphanumeric' | 'Byte' | 'Kanji';
}

/**
 * Opções de geração de QR Code
 */
export interface IQRGenerationOptions extends IQRConfig {
  data: string;
  width?: number;
  height?: number;
  margin?: number;
}

/**
 * Dados PIX para geração de BR-Code
 */
export interface IPixData {
  key: string;
  nameReceiver: string;
  cityReceiver: string;
  amount?: number;
  zipcodeReceiver?: string;
  identification?: string;
  description?: string;
  defaultUrlPix?: string;
  singleTransaction?: boolean;
}

/**
 * Tipos de chave PIX
 */
export enum PixKeyType {
  CPF = 'cpf',
  CNPJ = 'cnpj',
  EMAIL = 'email',
  PHONE = 'phone',
  RANDOM = 'random'
}

/**
 * Interface para validadores PIX
 */
export interface IPixValidator {
  /**
   * Valida CPF
   * @param cpf CPF a ser validado
   * @returns true se válido
   */
  validateCPF(cpf: string): boolean;

  /**
   * Valida CNPJ
   * @param cnpj CNPJ a ser validado
   * @returns true se válido
   */
  validateCNPJ(cnpj: string): boolean;

  /**
   * Valida telefone
   * @param phone Telefone a ser validado
   * @returns true se válido
   */
  validatePhone(phone: string): boolean;

  /**
   * Valida email
   * @param email Email a ser validado
   * @returns true se válido
   */
  validateEmail(email: string): boolean;
}

/**
 * Interface para parsers de BR-Code
 */
export interface IBRCodeParser {
  /**
   * Faz parse de um BR-Code
   * @param brCode BR-Code a ser parseado
   * @returns Dados parseados
   */
  parse(brCode: string): IPixData;

  /**
   * Valida formato de BR-Code
   * @param brCode BR-Code a ser validado
   * @returns true se válido
   */
  isValidBRCode(brCode: string): boolean;
}
