import { Options, Gradient } from "../../types";

/**
 * Interface para engines de estilização
 * Implementa o princípio da Responsabilidade Única (SRP)
 */
export interface IStyleEngine {
  /**
   * Aplica estilos ao elemento
   * @param element Elemento a ser estilizado
   * @param options Opções de estilo
   */
  applyStyles(element: any, options: Options): void;

  /**
   * Processa gradientes
   * @param gradient Configuração do gradiente
   * @returns Definição processada do gradiente
   */
  processGradient(gradient: Gradient): any;

  /**
   * Valida opções de estilo
   * @param options Opções a serem validadas
   * @returns true se válidas
   */
  validateOptions(options: Options): boolean;
}

/**
 * Interface para processadores de estilo específicos
 */
export interface IStyleProcessor {
  /**
   * Processa um tipo específico de estilo
   * @param value Valor do estilo
   * @param context Contexto de aplicação
   * @returns Estilo processado
   */
  process(value: any, context: IStyleContext): any;

  /**
   * Verifica se pode processar o tipo de estilo
   * @param type Tipo de estilo
   * @returns true se pode processar
   */
  canProcess(type: string): boolean;
}

/**
 * Interface para contexto de estilização
 */
export interface IStyleContext {
  element: any;
  options: Options;
  moduleSize: number;
  position: { x: number; y: number };
}

/**
 * Interface para aplicadores de estilo
 */
export interface IStyleApplier {
  /**
   * Aplica estilo de pontos
   * @param context Contexto de estilização
   */
  applyDotStyle(context: IStyleContext): void;

  /**
   * Aplica estilo de cantos
   * @param context Contexto de estilização
   */
  applyCornerStyle(context: IStyleContext): void;

  /**
   * Aplica estilo de fundo
   * @param context Contexto de estilização
   */
  applyBackgroundStyle(context: IStyleContext): void;
}
