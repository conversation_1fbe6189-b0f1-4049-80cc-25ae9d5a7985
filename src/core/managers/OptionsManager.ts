import mergeDeep from "../../tools/merge";
import sanitizeOptions from "../../tools/sanitizeOptions";
import defaultOptions, { RequiredOptions } from "../QROptions";
import { Options } from "../../types";

/**
 * Manages QR Code options and configuration
 * Implements Single Responsibility Principle - only handles options management
 */
export class OptionsManager {
  private options: RequiredOptions;

  constructor(initialOptions?: Partial<Options>) {
    this.options = initialOptions 
      ? sanitizeOptions(mergeDeep(defaultOptions, initialOptions) as RequiredOptions) 
      : defaultOptions;
  }

  /**
   * Gets current options
   * @returns Current options
   */
  getOptions(): RequiredOptions {
    return this.options;
  }

  /**
   * Updates options with new values
   * @param newOptions New options to merge
   */
  updateOptions(newOptions: Partial<Options>): void {
    this.options = sanitizeOptions(mergeDeep(this.options, newOptions) as RequiredOptions);
  }

  /**
   * Resets options to defaults
   */
  resetToDefaults(): void {
    this.options = { ...defaultOptions };
  }

  /**
   * Validates current options
   * @returns true if options are valid
   */
  validateOptions(): boolean {
    // Basic validation - can be extended
    return !!(this.options.width && this.options.height && this.options.data);
  }

  /**
   * Gets a specific option value
   * @param path Option path (e.g., 'qrOptions.typeNumber')
   * @returns Option value
   */
  getOption<T>(path: string): T | undefined {
    return this.getNestedValue(this.options, path);
  }

  /**
   * Sets a specific option value
   * @param path Option path
   * @param value New value
   */
  setOption(path: string, value: any): void {
    this.setNestedValue(this.options, path, value);
  }

  /**
   * Gets nested value from object using dot notation
   * @param obj Object to search
   * @param path Dot notation path
   * @returns Found value
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * Sets nested value in object using dot notation
   * @param obj Object to modify
   * @param path Dot notation path
   * @param value Value to set
   */
  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop()!;
    const target = keys.reduce((current, key) => {
      if (!current[key]) current[key] = {};
      return current[key];
    }, obj);
    target[lastKey] = value;
  }
}
