import { IQRCodeManager, IQRCodeConfig } from "../interfaces";
import { IQRRenderer } from "../interfaces/IQRRenderer";
import { IStyleEngine } from "../interfaces/IStyleEngine";
import { IImageProcessor } from "../interfaces/IImageProcessor";
import { IQRGenerator } from "../interfaces/IQRGenerator";
import { RendererFactory } from "../factories/RendererFactory";
import { StyleFactory } from "../factories/StyleFactory";
import { ImageProcessorFactory } from "../factories/ImageProcessorFactory";
import { GeneratorFactory } from "../factories/GeneratorFactory";
import { UnifiedOptions } from "../../types/unified";
import { EventEmitter } from "events";

/**
 * Main QR Code manager that orchestrates all components
 * Implements Facade Pattern and manages component lifecycle
 */
export class QRManager extends EventEmitter implements IQRCodeManager {
  private renderer?: IQRRenderer;
  private styleEngine?: IStyleEngine;
  private imageProcessor?: IImageProcessor;
  private generator?: IQRGenerator;
  private config?: IQRCodeConfig;
  private currentOptions?: UnifiedOptions;
  private currentQR?: any;

  /**
   * Initializes the manager with configuration
   * @param config Configuration object
   */
  initialize(config: IQRCodeConfig): void {
    this.config = config;
    
    // Create components using factories
    this.renderer = RendererFactory.create(config.rendererType, {
      width: config.defaultOptions?.width || 300,
      height: config.defaultOptions?.height || 300,
      margin: config.defaultOptions?.margin || 0,
      type: config.rendererType
    });

    this.styleEngine = StyleFactory.createEngine(config.styleEngineType);
    this.imageProcessor = ImageProcessorFactory.create(config.imageProcessorType);
    this.generator = GeneratorFactory.create(config.generatorType);

    this.emit('initialized', config);
  }

  /**
   * Generates and renders a QR code
   * @param data Data for the QR code
   * @param options Rendering options
   * @returns Promise that resolves when complete
   */
  async generateQRCode(data: string, options: UnifiedOptions = {}): Promise<any> {
    if (!this.isInitialized()) {
      throw new Error('QRManager must be initialized before use');
    }

    try {
      this.emit('beforeRender', options);

      // Merge with default options
      const mergedOptions = this.mergeOptions(options);
      this.currentOptions = mergedOptions;

      // Generate QR code data
      const qrGenerationOptions = {
        data,
        typeNumber: mergedOptions.qrOptions?.typeNumber || 0,
        errorCorrectionLevel: mergedOptions.qrOptions?.errorCorrectionLevel || 'M',
        mode: mergedOptions.qrOptions?.mode
      };

      this.currentQR = this.generator!.generate(data, qrGenerationOptions);

      // Apply styles
      if (this.styleEngine && mergedOptions.styles) {
        this.styleEngine.applyStyles(this.currentQR, mergedOptions);
      }

      // Render QR code
      await this.renderer!.render(this.currentQR, mergedOptions);

      const element = this.renderer!.getElement();
      this.emit('afterRender', element);

      return element;
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Updates existing QR code with new options
   * @param options New options
   * @returns Promise that resolves when updated
   */
  async updateQRCode(options: UnifiedOptions): Promise<any> {
    if (!this.currentQR) {
      throw new Error('No QR code to update. Generate a QR code first.');
    }

    const mergedOptions = this.mergeOptions(options);
    this.currentOptions = mergedOptions;

    // Apply new styles
    if (this.styleEngine) {
      this.styleEngine.applyStyles(this.currentQR, mergedOptions);
    }

    // Re-render with new options
    await this.renderer!.render(this.currentQR, mergedOptions);

    const element = this.renderer!.getElement();
    this.emit('afterRender', element);

    return element;
  }

  /**
   * Exports QR code in specified format
   * @param format Export format
   * @param options Export options
   * @returns Promise with exported data
   */
  async exportQRCode(format: string, options: any = {}): Promise<any> {
    if (!this.currentQR || !this.renderer) {
      throw new Error('No QR code to export. Generate a QR code first.');
    }

    try {
      this.emit('beforeExport', format);

      const element = this.renderer.getElement();
      if (!element) {
        throw new Error('No rendered element to export');
      }

      let exportedData;

      if (format.toLowerCase() === 'svg' && element instanceof SVGElement) {
        exportedData = new XMLSerializer().serializeToString(element);
      } else if (this.imageProcessor) {
        // Convert to image format using image processor
        const canvas = element as HTMLCanvasElement;
        const imageData = canvas.toDataURL('image/png');
        const buffer = Buffer.from(imageData.split(',')[1], 'base64');
        
        exportedData = await this.imageProcessor.process(buffer, {
          format: format.toLowerCase(),
          quality: options.quality,
          width: options.width,
          height: options.height
        });
      } else {
        throw new Error(`Export format '${format}' is not supported`);
      }

      this.emit('afterExport', exportedData);
      return exportedData;
    } catch (error) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Gets the current rendered element
   * @returns Current element or null
   */
  getCurrentElement(): any {
    return this.renderer?.getElement() || null;
  }

  /**
   * Gets the current options
   * @returns Current options
   */
  getCurrentOptions(): UnifiedOptions | undefined {
    return this.currentOptions;
  }

  /**
   * Cleans up resources
   */
  dispose(): void {
    this.renderer?.clear();
    this.removeAllListeners();
    
    this.renderer = undefined;
    this.styleEngine = undefined;
    this.imageProcessor = undefined;
    this.generator = undefined;
    this.currentQR = undefined;
    this.currentOptions = undefined;
  }

  /**
   * Checks if the manager is initialized
   * @returns true if initialized
   */
  private isInitialized(): boolean {
    return !!(this.renderer && this.styleEngine && this.imageProcessor && this.generator);
  }

  /**
   * Merges options with defaults
   * @param options Options to merge
   * @returns Merged options
   */
  private mergeOptions(options: UnifiedOptions): UnifiedOptions {
    return {
      ...this.config?.defaultOptions,
      ...options
    };
  }
}
