import QRSVG from "../QRSVG";
import { RequiredOptions } from "../QROptions";
import { QRCode, ExtensionFunction, Window } from "../../types";
import { ElementManager } from "./ElementManager";
import getMimeType from "../../tools/getMimeType";
import { Canvas as NodeCanvas, Image } from "canvas";

/**
 * Manages QR Code rendering operations
 * Implements Single Responsibility Principle - only handles rendering logic
 */
export class RenderingManager {
  private window: Window;
  private elementManager: ElementManager;
  private extension?: ExtensionFunction;
  private canvasDrawingPromise?: Promise<void>;
  private svgDrawingPromise?: Promise<void>;

  constructor(window: Window, elementManager: ElementManager) {
    this.window = window;
    this.elementManager = elementManager;
  }

  /**
   * Sets up SVG rendering
   * @param qr QR Code instance
   * @param options Rendering options
   */
  setupSvg(qr: QRCode, options: RequiredOptions): void {
    const qrSVG = new QRSVG(options, this.window);
    const svg = qrSVG.getElement();
    
    this.elementManager.setSvg(svg);
    
    this.svgDrawingPromise = qrSVG.drawQR(qr).then(() => {
      const svgElement = this.elementManager.getSvg();
      if (svgElement && this.extension) {
        this.extension(svgElement, options);
      }
    });
  }

  /**
   * Sets up Canvas rendering
   * @param qr QR Code instance
   * @param options Rendering options
   */
  setupCanvas(qr: QRCode, options: RequiredOptions): void {
    // Create canvas element
    const canvas = this.elementManager.createCanvas(
      options.width, 
      options.height, 
      options.nodeCanvas
    );

    // Setup SVG first (canvas rendering uses SVG as intermediate step)
    this.setupSvg(qr, options);

    // Convert SVG to canvas
    this.canvasDrawingPromise = this.svgDrawingPromise?.then(() => {
      const svg = this.elementManager.getSvg();
      if (!svg) return;

      const xml = new this.window.XMLSerializer().serializeToString(svg);
      const svg64 = btoa(xml);
      const image64 = `data:${getMimeType('svg')};base64,${svg64}`;

      if (options.nodeCanvas?.loadImage) {
        return this.renderToNodeCanvas(image64, options, canvas as NodeCanvas);
      } else {
        return this.renderToDomCanvas(image64, canvas as HTMLCanvasElement);
      }
    });
  }

  /**
   * Renders SVG to Node.js canvas
   * @param imageData Base64 image data
   * @param options Rendering options
   * @param canvas Node canvas instance
   */
  private async renderToNodeCanvas(
    imageData: string, 
    options: RequiredOptions, 
    canvas: NodeCanvas
  ): Promise<void> {
    if (!options.nodeCanvas?.loadImage) {
      throw new Error('Node canvas loadImage function not available');
    }

    const image = await options.nodeCanvas.loadImage(imageData) as Image;
    
    // Fix blurry SVG
    image.width = options.width;
    image.height = options.height;
    
    const ctx = canvas.getContext("2d");
    ctx?.drawImage(image, 0, 0);
  }

  /**
   * Renders SVG to DOM canvas
   * @param imageData Base64 image data
   * @param canvas DOM canvas element
   */
  private async renderToDomCanvas(imageData: string, canvas: HTMLCanvasElement): Promise<void> {
    return new Promise((resolve) => {
      const image = new this.window.Image();
      
      image.onload = (): void => {
        const ctx = canvas.getContext("2d");
        ctx?.drawImage(image, 0, 0);
        resolve();
      };

      image.src = imageData;
    });
  }

  /**
   * Gets rendered element with proper setup
   * @param extension File extension
   * @param qr QR Code instance
   * @param options Rendering options
   * @returns Promise with rendered element
   */
  async getElement(
    extension: string = "png", 
    qr: QRCode, 
    options: RequiredOptions
  ): Promise<HTMLElement | SVGElement | HTMLCanvasElement | NodeCanvas | null> {
    if (!qr) {
      throw new Error("QR code is empty");
    }

    if (extension.toLowerCase() === "svg") {
      if (!this.elementManager.getSvg() || !this.svgDrawingPromise) {
        this.setupSvg(qr, options);
      }
      await this.svgDrawingPromise;
      return this.elementManager.getSvg() || null;
    } else {
      const canvas = this.elementManager.getDomCanvas() || this.elementManager.getNodeCanvas();
      if (!canvas || !this.canvasDrawingPromise) {
        this.setupCanvas(qr, options);
      }
      await this.canvasDrawingPromise;
      return this.elementManager.getDomCanvas() || this.elementManager.getNodeCanvas() || null;
    }
  }

  /**
   * Sets extension function
   * @param extension Extension function
   */
  setExtension(extension: ExtensionFunction): void {
    this.extension = extension;
  }

  /**
   * Removes extension function
   */
  removeExtension(): void {
    this.extension = undefined;
  }

  /**
   * Gets current extension function
   * @returns Current extension function
   */
  getExtension(): ExtensionFunction | undefined {
    return this.extension;
  }

  /**
   * Checks if SVG rendering is ready
   * @returns true if SVG is ready
   */
  isSvgReady(): boolean {
    return !!(this.elementManager.getSvg() && this.svgDrawingPromise);
  }

  /**
   * Checks if Canvas rendering is ready
   * @returns true if Canvas is ready
   */
  isCanvasReady(): boolean {
    const canvas = this.elementManager.getDomCanvas() || this.elementManager.getNodeCanvas();
    return !!(canvas && this.canvasDrawingPromise);
  }

  /**
   * Waits for SVG rendering to complete
   * @returns Promise that resolves when SVG is ready
   */
  async waitForSvg(): Promise<void> {
    if (this.svgDrawingPromise) {
      await this.svgDrawingPromise;
    }
  }

  /**
   * Waits for Canvas rendering to complete
   * @returns Promise that resolves when Canvas is ready
   */
  async waitForCanvas(): Promise<void> {
    if (this.canvasDrawingPromise) {
      await this.canvasDrawingPromise;
    }
  }

  /**
   * Clears rendering state
   */
  clear(): void {
    this.extension = undefined;
    this.canvasDrawingPromise = undefined;
    this.svgDrawingPromise = undefined;
  }
}
