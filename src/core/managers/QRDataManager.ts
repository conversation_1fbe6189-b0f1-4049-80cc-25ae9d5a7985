import getMode from "../../tools/getMode";
import { QRCode, Options } from "../../types";
import qrcode from "qrcode-generator";

/**
 * Manages QR Code data generation and validation
 * Implements Single Responsibility Principle - only handles QR data management
 */
export class QRDataManager {
  private qr?: QRCode;

  /**
   * Generates QR Code data
   * @param data Data to encode
   * @param options QR options
   * @returns Generated QR Code instance
   */
  generateQR(data: string, options: Options): QRCode {
    if (!data) {
      throw new Error("QR code data cannot be empty");
    }

    const typeNumber = options.qrOptions?.typeNumber || 0;
    const errorCorrectionLevel = options.qrOptions?.errorCorrectionLevel || 'M';
    const mode = options.qrOptions?.mode || getMode(data);

    this.qr = qrcode(typeNumber, errorCorrectionLevel);
    this.qr.addData(data, mode);
    this.qr.make();

    return this.qr;
  }

  /**
   * Gets the current QR Code instance
   * @returns Current QR Code instance
   */
  getCurrentQR(): QRCode | undefined {
    return this.qr;
  }

  /**
   * Validates QR Code data
   * @param data Data to validate
   * @returns true if data is valid
   */
  validateData(data: string): boolean {
    if (!data || typeof data !== 'string') {
      return false;
    }

    // Basic validation - can be extended with more sophisticated checks
    return data.length > 0 && data.length <= 4296; // QR Code maximum capacity
  }

  /**
   * Gets optimal mode for data
   * @param data Data to analyze
   * @returns Optimal mode
   */
  getOptimalMode(data: string): string {
    return getMode(data);
  }

  /**
   * Estimates QR Code size requirements
   * @param data Data to analyze
   * @param errorCorrectionLevel Error correction level
   * @returns Estimated type number
   */
  estimateTypeNumber(data: string, errorCorrectionLevel: string = 'M'): number {
    // This is a simplified estimation - real implementation would be more complex
    const dataLength = data.length;
    
    if (dataLength <= 25) return 1;
    if (dataLength <= 47) return 2;
    if (dataLength <= 77) return 3;
    if (dataLength <= 114) return 4;
    if (dataLength <= 154) return 5;
    if (dataLength <= 195) return 6;
    if (dataLength <= 224) return 7;
    if (dataLength <= 279) return 8;
    if (dataLength <= 335) return 9;
    if (dataLength <= 395) return 10;
    
    // For larger data, return a reasonable default
    return Math.min(40, Math.ceil(dataLength / 40));
  }

  /**
   * Gets QR Code module count
   * @returns Module count or 0 if no QR generated
   */
  getModuleCount(): number {
    return this.qr?.getModuleCount() || 0;
  }

  /**
   * Checks if a module is dark
   * @param row Module row
   * @param col Module column
   * @returns true if module is dark
   */
  isDark(row: number, col: number): boolean {
    return this.qr?.isDark(row, col) || false;
  }

  /**
   * Gets QR Code capacity information
   * @param typeNumber Type number
   * @param errorCorrectionLevel Error correction level
   * @returns Capacity information
   */
  getCapacityInfo(typeNumber: number, errorCorrectionLevel: string): {
    numeric: number;
    alphanumeric: number;
    byte: number;
    kanji: number;
  } {
    // Simplified capacity calculation - real implementation would use lookup tables
    const baseCapacity = typeNumber * 10;
    const correctionMultiplier = {
      'L': 1.0,
      'M': 0.8,
      'Q': 0.65,
      'H': 0.5
    }[errorCorrectionLevel] || 0.8;

    const adjustedCapacity = Math.floor(baseCapacity * correctionMultiplier);

    return {
      numeric: Math.floor(adjustedCapacity * 1.2),
      alphanumeric: adjustedCapacity,
      byte: Math.floor(adjustedCapacity * 0.8),
      kanji: Math.floor(adjustedCapacity * 0.5)
    };
  }

  /**
   * Clears current QR data
   */
  clear(): void {
    this.qr = undefined;
  }
}
