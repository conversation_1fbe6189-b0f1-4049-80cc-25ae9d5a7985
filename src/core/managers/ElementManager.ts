import { FileExtension, Window } from "../../types";
// import getMimeType from "../../tools/getMimeType";
import { Canvas as NodeCanvas } from "canvas";

/**
 * Manages DOM elements and rendering contexts
 * Implements Single Responsibility Principle - only handles element management
 */
export class ElementManager {
  private window: Window;
  private container?: HTMLElement;
  private domCanvas?: HTMLCanvasElement;
  private nodeCanvas?: NodeCanvas;
  private svg?: SVGElement;

  constructor(window: Window) {
    this.window = window;
  }

  /**
   * Sets the container element
   * @param container Container element
   */
  setContainer(container?: HTMLElement): void {
    this.container = container;
  }

  /**
   * Gets the container element
   * @returns Container element
   */
  getContainer(): HTMLElement | undefined {
    return this.container;
  }

  /**
   * Sets the DOM canvas element
   * @param canvas Canvas element
   */
  setDomCanvas(canvas: HTMLCanvasElement): void {
    this.domCanvas = canvas;
  }

  /**
   * Gets the DOM canvas element
   * @returns DOM canvas element
   */
  getDomCanvas(): HTMLCanvasElement | undefined {
    return this.domCanvas;
  }

  /**
   * Sets the Node canvas element
   * @param canvas Node canvas element
   */
  setNodeCanvas(canvas: NodeCanvas): void {
    this.nodeCanvas = canvas;
  }

  /**
   * Gets the Node canvas element
   * @returns Node canvas element
   */
  getNodeCanvas(): NodeCanvas | undefined {
    return this.nodeCanvas;
  }

  /**
   * Sets the SVG element
   * @param svg SVG element
   */
  setSvg(svg: SVGElement): void {
    this.svg = svg;
  }

  /**
   * Gets the SVG element
   * @returns SVG element
   */
  getSvg(): SVGElement | undefined {
    return this.svg;
  }

  /**
   * Gets the appropriate element based on extension
   * @param extension File extension
   * @returns Element for the specified extension
   */
  getElement(extension: FileExtension = "png"): HTMLElement | SVGElement | HTMLCanvasElement | NodeCanvas | undefined {
    if (extension.toLowerCase() === "svg") {
      return this.svg;
    } else {
      return this.domCanvas || this.nodeCanvas;
    }
  }

  /**
   * Clears the container
   */
  clearContainer(): void {
    if (this.container) {
      this.container.innerHTML = "";
    }
  }

  /**
   * Appends element to container
   * @param type Element type ('canvas' or 'svg')
   */
  appendToContainer(type: 'canvas' | 'svg'): void {
    if (!this.container) {
      return;
    }

    if (typeof this.container.appendChild !== "function") {
      throw new Error("Container should be a single DOM node");
    }

    if (type === 'canvas') {
      if (this.domCanvas) {
        this.container.appendChild(this.domCanvas);
      }
    } else {
      if (this.svg) {
        this.container.appendChild(this.svg);
      }
    }
  }

  /**
   * Creates a canvas element
   * @param width Canvas width
   * @param height Canvas height
   * @param nodeCanvas Node canvas factory
   * @returns Created canvas element
   */
  createCanvas(width: number, height: number, nodeCanvas?: any): HTMLCanvasElement | NodeCanvas {
    if (nodeCanvas?.createCanvas) {
      const canvas = nodeCanvas.createCanvas(width, height);
      canvas.width = width;
      canvas.height = height;
      this.setNodeCanvas(canvas);
      return canvas;
    } else {
      const canvas = document.createElement("canvas");
      canvas.width = width;
      canvas.height = height;
      this.setDomCanvas(canvas);
      return canvas;
    }
  }

  /**
   * Serializes SVG element to string
   * @param svg SVG element to serialize
   * @returns Serialized SVG string
   */
  serializeSvg(svg: SVGElement): string {
    const serializer = new this.window.XMLSerializer();
    const source = serializer.serializeToString(svg);
    return `<?xml version="1.0" standalone="no"?>\r\n${source}`;
  }

  /**
   * Converts canvas to buffer
   * @param canvas Canvas element
   * @param mimeType MIME type
   * @returns Promise with buffer
   */
  canvasToBuffer(canvas: any, mimeType: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      if ('toBuffer' in canvas) {
        try {
          resolve(canvas.toBuffer(mimeType));
        } catch (error) {
          reject(error);
        }
      } else {
        reject(new Error('Canvas does not support toBuffer method'));
      }
    });
  }

  /**
   * Converts canvas to blob
   * @param canvas Canvas element
   * @param mimeType MIME type
   * @returns Promise with blob
   */
  canvasToBlob(canvas: HTMLCanvasElement, mimeType: string): Promise<Blob | null> {
    return new Promise((resolve) => {
      canvas.toBlob(resolve, mimeType, 1);
    });
  }

  /**
   * Clears all elements
   */
  clear(): void {
    this.container = undefined;
    this.domCanvas = undefined;
    this.nodeCanvas = undefined;
    this.svg = undefined;
  }
}
