import { FileExtension, QRCode, DownloadOptions } from "../../types";
import { RequiredOptions } from "../QROptions";
import { ElementManager } from "./ElementManager";
import { RenderingManager } from "./RenderingManager";
import getMimeType from "../../tools/getMimeType";
import downloadURI from "../../tools/downloadURI";

/**
 * Manages QR Code export operations
 * Implements Single Responsibility Principle - only handles export logic
 */
export class ExportManager {
  private elementManager: ElementManager;
  private renderingManager: RenderingManager;

  constructor(elementManager: ElementManager, renderingManager: RenderingManager) {
    this.elementManager = elementManager;
    this.renderingManager = renderingManager;
  }

  /**
   * Gets raw data for export
   * @param extension File extension
   * @param qr QR Code instance
   * @param options Rendering options
   * @returns Promise with raw data
   */
  async getRawData(
    extension: FileExtension = "png", 
    qr: QRCode, 
    options: RequiredOptions
  ): Promise<Blob | Buffer | null> {
    const element = await this.renderingManager.getElement(extension, qr, options);
    const mimeType = getMimeType(extension);

    if (!element) {
      return null;
    }

    if (extension.toLowerCase() === "svg") {
      return this.exportSvg(element as SVGElement, mimeType);
    } else {
      return this.exportCanvas(element as any, mimeType);
    }
  }

  /**
   * Downloads QR code file
   * @param downloadOptions Download options
   * @param qr QR Code instance
   * @param options Rendering options
   */
  async download(
    downloadOptions: Partial<DownloadOptions> | string | undefined,
    qr: QRCode,
    options: RequiredOptions
  ): Promise<void> {
    let extension = "png" as FileExtension;
    let name = "qr";

    // Handle deprecated string parameter
    if (typeof downloadOptions === "string") {
      extension = downloadOptions as FileExtension;
      console.warn(
        "Extension is deprecated as argument for 'download' method, please pass object { name: '...', extension: '...' } as argument"
      );
    } else if (typeof downloadOptions === "object" && downloadOptions !== null) {
      if (downloadOptions.name) {
        name = downloadOptions.name;
      }
      if (downloadOptions.extension) {
        extension = downloadOptions.extension;
      }
    }

    const element = await this.renderingManager.getElement(extension, qr, options);

    if (!element) {
      return;
    }

    if (extension.toLowerCase() === "svg") {
      await this.downloadSvg(element as SVGElement, name);
    } else {
      await this.downloadCanvas(element as HTMLCanvasElement, name, extension);
    }
  }

  /**
   * Exports SVG element
   * @param svg SVG element
   * @param mimeType MIME type
   * @returns Promise with exported data
   */
  private async exportSvg(svg: SVGElement, mimeType: string): Promise<Blob | Buffer> {
    const svgString = this.elementManager.serializeSvg(svg);
    
    if (typeof Blob !== "undefined") {
      return new Blob([svgString], { type: mimeType });
    } else {
      return Buffer.from(svgString);
    }
  }

  /**
   * Exports canvas element
   * @param canvas Canvas element
   * @param mimeType MIME type
   * @returns Promise with exported data
   */
  private async exportCanvas(canvas: any, mimeType: string): Promise<Buffer | Blob | null> {
    return new Promise((resolve, reject) => {
      if ('toBuffer' in canvas) {
        // Node.js canvas
        try {
          if (mimeType === "image/png" || mimeType === "image/jpeg" || mimeType === "application/pdf") {
            resolve(canvas.toBuffer(mimeType));
          } else {
            reject(new Error("Unsupported extension"));
          }
        } catch (error) {
          reject(error);
        }
      } else if ('toBlob' in canvas) {
        // DOM canvas
        canvas.toBlob(resolve, mimeType, 1);
      } else {
        reject(new Error("Canvas does not support export"));
      }
    });
  }

  /**
   * Downloads SVG file
   * @param svg SVG element
   * @param name File name
   */
  private async downloadSvg(svg: SVGElement, name: string): Promise<void> {
    const serializer = new XMLSerializer();
    let source = serializer.serializeToString(svg);
    
    source = '<?xml version="1.0" standalone="no"?>\r\n' + source;
    const url = `data:${getMimeType('svg')};charset=utf-8,${encodeURIComponent(source)}`;
    downloadURI(url, `${name}.svg`);
  }

  /**
   * Downloads canvas file
   * @param canvas Canvas element
   * @param name File name
   * @param extension File extension
   */
  private async downloadCanvas(canvas: HTMLCanvasElement, name: string, extension: FileExtension): Promise<void> {
    const url = canvas.toDataURL(getMimeType(extension));
    downloadURI(url, `${name}.${extension}`);
  }

  /**
   * Converts element to base64
   * @param extension File extension
   * @param qr QR Code instance
   * @param options Rendering options
   * @returns Promise with base64 string
   */
  async toBase64(
    extension: FileExtension = "png",
    qr: QRCode,
    options: RequiredOptions
  ): Promise<string> {
    const rawData = await this.getRawData(extension, qr, options);
    
    if (!rawData) {
      throw new Error("Failed to generate raw data");
    }

    if (rawData instanceof Blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          resolve(result.split(',')[1]); // Remove data URL prefix
        };
        reader.onerror = reject;
        reader.readAsDataURL(rawData);
      });
    } else {
      // Buffer
      return rawData.toString('base64');
    }
  }

  /**
   * Converts element to data URL
   * @param extension File extension
   * @param qr QR Code instance
   * @param options Rendering options
   * @returns Promise with data URL
   */
  async toDataURL(
    extension: FileExtension = "png",
    qr: QRCode,
    options: RequiredOptions
  ): Promise<string> {
    const element = await this.renderingManager.getElement(extension, qr, options);
    
    if (!element) {
      throw new Error("No element to convert");
    }

    if (extension.toLowerCase() === "svg") {
      const svgString = this.elementManager.serializeSvg(element as SVGElement);
      const base64 = btoa(svgString);
      return `data:${getMimeType(extension)};base64,${base64}`;
    } else {
      const canvas = element as HTMLCanvasElement;
      return canvas.toDataURL(getMimeType(extension));
    }
  }

  /**
   * Gets supported export formats
   * @returns Array of supported formats
   */
  getSupportedFormats(): FileExtension[] {
    return ['png', 'jpeg', 'svg', 'webp'];
  }

  /**
   * Checks if format is supported
   * @param extension File extension
   * @returns true if supported
   */
  isFormatSupported(extension: string): boolean {
    return this.getSupportedFormats().includes(extension as FileExtension);
  }
}
