import { FileExtension, QRCode, Options, DownloadOptions, ExtensionFunction, Window } from "../types";
import { OptionsManager } from "./managers/OptionsManager";
import { ElementManager } from "./managers/ElementManager";
import { QRDataManager } from "./managers/QRDataManager";
import { RenderingManager } from "./managers/RenderingManager";
import { ExportManager } from "./managers/ExportManager";
import drawTypes from "../constants/drawTypes";

declare const window: Window;

/**
 * Refactored QR Code Styling class following SOLID principles
 * Each responsibility is delegated to specialized managers
 */
export default class QRCodeStylingRefactored {
  private optionsManager: OptionsManager;
  private elementManager: ElementManager;
  private qrDataManager: QRDataManager;
  private renderingManager: RenderingManager;
  private exportManager: ExportManager;
  private window: Window;

  constructor(options?: Partial<Options>) {
    // Initialize window context
    if (options?.jsdom) {
      this.window = new options.jsdom("", { resources: "usable" }).window;
    } else {
      this.window = window;
    }

    // Initialize managers
    this.optionsManager = new OptionsManager(options);
    this.elementManager = new ElementManager(this.window);
    this.qrDataManager = new QRDataManager();
    this.renderingManager = new RenderingManager(this.window, this.elementManager);
    this.exportManager = new ExportManager(this.elementManager, this.renderingManager);

    // Initial update
    this.update();
  }

  /**
   * Updates QR code with new options
   * @param options New options to apply
   */
  update(options?: Partial<Options>): void {
    // Clear container
    this.elementManager.clearContainer();

    // Update options
    if (options) {
      this.optionsManager.updateOptions(options);
    }

    const currentOptions = this.optionsManager.getOptions();

    // Validate data
    if (!currentOptions.data) {
      return;
    }

    // Generate QR data
    const qr = this.qrDataManager.generateQR(currentOptions.data, currentOptions);

    // Setup rendering based on type
    if (currentOptions.type === drawTypes.canvas) {
      this.renderingManager.setupCanvas(qr, currentOptions);
    } else {
      this.renderingManager.setupSvg(qr, currentOptions);
    }

    // Append to container if available
    const container = this.elementManager.getContainer();
    if (container) {
      this.elementManager.appendToContainer(currentOptions.type);
    }
  }

  /**
   * Appends QR code to container
   * @param container Container element
   */
  append(container?: HTMLElement): void {
    if (!container) {
      return;
    }

    if (typeof container.appendChild !== "function") {
      throw new Error("Container should be a single DOM node");
    }

    this.elementManager.setContainer(container);
    const options = this.optionsManager.getOptions();
    this.elementManager.appendToContainer(options.type);
  }

  /**
   * Applies extension function
   * @param extension Extension function
   */
  applyExtension(extension: ExtensionFunction): void {
    if (!extension) {
      throw new Error("Extension function should be defined.");
    }

    this.renderingManager.setExtension(extension);
    this.update();
  }

  /**
   * Removes extension function
   */
  deleteExtension(): void {
    this.renderingManager.removeExtension();
    this.update();
  }

  /**
   * Gets raw data for the QR code
   * @param extension File extension
   * @returns Promise with raw data
   */
  async getRawData(extension: FileExtension = "png"): Promise<Blob | Buffer | null> {
    const qr = this.qrDataManager.getCurrentQR();
    if (!qr) {
      throw new Error("QR code is empty");
    }

    return this.exportManager.getRawData(extension, qr, this.optionsManager.getOptions());
  }

  /**
   * Downloads the QR code
   * @param downloadOptions Download options
   */
  async download(downloadOptions?: Partial<DownloadOptions> | string): Promise<void> {
    const qr = this.qrDataManager.getCurrentQR();
    if (!qr) {
      throw new Error("QR code is empty");
    }

    if (typeof Blob === "undefined") {
      throw new Error("Cannot download in Node.js, call getRawData instead.");
    }

    return this.exportManager.download(downloadOptions, qr, this.optionsManager.getOptions());
  }

  /**
   * Gets current options
   * @returns Current options
   */
  getOptions(): Options {
    return this.optionsManager.getOptions();
  }

  /**
   * Gets specific option value
   * @param path Option path
   * @returns Option value
   */
  getOption<T>(path: string): T | undefined {
    return this.optionsManager.getOption<T>(path);
  }

  /**
   * Sets specific option value
   * @param path Option path
   * @param value New value
   */
  setOption(path: string, value: any): void {
    this.optionsManager.setOption(path, value);
    this.update();
  }

  /**
   * Gets current QR data
   * @returns Current QR instance
   */
  getQRData(): QRCode | undefined {
    return this.qrDataManager.getCurrentQR();
  }

  /**
   * Validates current configuration
   * @returns true if configuration is valid
   */
  isValid(): boolean {
    return this.optionsManager.validateOptions();
  }

  /**
   * Gets rendering statistics
   * @returns Rendering statistics
   */
  getStats(): {
    moduleCount: number;
    dataLength: number;
    errorCorrectionLevel: string;
    typeNumber: number;
  } {
    const options = this.optionsManager.getOptions();

    return {
      moduleCount: this.qrDataManager.getModuleCount(),
      dataLength: options.data?.length || 0,
      errorCorrectionLevel: options.qrOptions.errorCorrectionLevel,
      typeNumber: options.qrOptions.typeNumber
    };
  }

  /**
   * Clears all data and resets to defaults
   */
  clear(): void {
    this.elementManager.clear();
    this.qrDataManager.clear();
    this.renderingManager.clear();
    this.optionsManager.resetToDefaults();
  }

  /**
   * Static method to clear container (for backward compatibility)
   * @param container Container to clear
   */
  static _clearContainer(container?: HTMLElement): void {
    if (container) {
      container.innerHTML = "";
    }
  }
}
