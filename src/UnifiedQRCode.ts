import { UnifiedQRCodeAPI, UnifiedOptions, AdvancedStyleOptions, ExportFormat, ExportOptions, DownloadOptions, FileExtension, OptionsConverter } from "./types/compatibility";
import { IPixData } from "./types/pix";
import { FrameStyle } from "./types/styles";
import { QRManager } from "./core/managers/QRManager";
import { IQRCodeConfig } from "./core/interfaces";
import { PixQRGenerator } from "./pix/generators/PixQRGenerator";
import { BRCodeParser } from "./pix/parsers/BRCodeParser";
import { PixValidators } from "./pix/validators/PixValidators";
import { AdvancedQRGenerator } from "./pix/generators/AdvancedQRGenerator";

/**
 * Unified QR Code class that combines qr-code-styling and qr-pix functionality
 * Implements Facade Pattern to provide a single interface for all QR code operations
 */
export class UnifiedQRCode implements UnifiedQRCodeAPI {
  private qrManager!: QRManager;
  private pixGenerator!: PixQRGenerator;
  private advancedGenerator!: AdvancedQRGenerator;
  private brCodeParser!: BRCodeParser;
  private pixValidators!: PixValidators;
  private currentOptions: UnifiedOptions;

  constructor(options: Partial<UnifiedOptions> = {}) {
    this.currentOptions = this.mergeWithDefaults(options);
    
    // Initialize core components
    this.initializeComponents();
  }

  /**
   * Initializes all internal components
   */
  private initializeComponents(): void {
    // Initialize QR Manager with appropriate configuration
    this.qrManager = new QRManager();
    const config: IQRCodeConfig = {
      rendererType: this.currentOptions.type || 'canvas',
      styleEngineType: this.currentOptions.features?.enableAdvancedStyling ? 'advanced' : 'standard',
      imageProcessorType: 'standard',
      generatorType: this.currentOptions.features?.enablePix ? 'pix' : 'standard',
      defaultOptions: this.currentOptions
    };
    this.qrManager.initialize(config);

    // Initialize PIX components
    this.pixGenerator = new PixQRGenerator();
    this.advancedGenerator = new AdvancedQRGenerator();
    this.brCodeParser = new BRCodeParser();
    this.pixValidators = new PixValidators();
  }

  /**
   * Updates QR code with new options
   * @param options New options to apply
   */
  update(options?: Partial<UnifiedOptions>): void {
    if (options) {
      this.currentOptions = { ...this.currentOptions, ...options };
    }

    // Handle PIX data if provided
    if (this.currentOptions.pix) {
      const brCode = this.pixGenerator.generateBRCode(this.currentOptions.pix);
      this.currentOptions.data = brCode;
    }

    // Convert to standard options for QR Manager
    const standardOptions = OptionsConverter.toQRCodeStyling(this.currentOptions);
    this.qrManager.generateQRCode(this.currentOptions.data || '', standardOptions);
  }

  /**
   * Appends QR code to container
   * @param container Container element
   */
  append(container?: HTMLElement): void {
    if (!container) {
      throw new Error('Container element is required');
    }

    const element = this.qrManager.getCurrentElement();
    if (element && container.appendChild) {
      container.appendChild(element);
    }
  }

  /**
   * Downloads QR code file
   * @param downloadOptions Download options
   */
  async download(downloadOptions?: DownloadOptions): Promise<void> {
    const options = downloadOptions || { name: 'qr-code', extension: 'png' };
    const rawData = await this.getRawData(options.extension);
    
    if (!rawData) {
      throw new Error('Failed to generate QR code data');
    }

    // Create download link
    const blob = rawData instanceof Buffer ? new Blob([rawData.buffer.slice(rawData.byteOffset, rawData.byteOffset + rawData.byteLength) as ArrayBuffer]) : rawData as Blob;
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${options.name}.${options.extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Gets raw QR code data
   * @param extension File extension
   * @returns Promise with raw data
   */
  async getRawData(extension: FileExtension = 'png'): Promise<Blob | Buffer | null> {
    if (this.currentOptions.features?.enableAdvancedStyling && this.currentOptions.advancedStyling) {
      // Use advanced generator for styled QR codes
      return await this.generateAdvancedQRCode(extension);
    } else {
      // Use standard QR manager
      return await this.qrManager.exportQRCode(extension);
    }
  }

  /**
   * Generates PIX BR-Code
   * @param pixData PIX transaction data
   * @returns BR-Code string
   */
  generatePix(pixData: IPixData): string {
    return this.pixGenerator.generateBRCode(pixData);
  }

  /**
   * Parses BR-Code to PIX data
   * @param brCode BR-Code string
   * @returns Parsed PIX data
   */
  parseBRCode(brCode: string): IPixData {
    return this.brCodeParser.parse(brCode);
  }

  /**
   * Validates PIX key
   * @param key PIX key to validate
   * @returns true if valid
   */
  validatePixKey(key: string): boolean {
    const validation = this.pixValidators.validatePixKey(key);
    return validation.isValid;
  }

  /**
   * Applies advanced styling
   * @param style Advanced style options
   */
  applyAdvancedStyle(style: AdvancedStyleOptions): void {
    this.currentOptions.advancedStyling = { ...this.currentOptions.advancedStyling, ...style };
    this.currentOptions.features = { ...this.currentOptions.features, enableAdvancedStyling: true };
    this.update();
  }

  /**
   * Sets predefined theme
   * @param theme Theme name
   */
  setTheme(theme: string): void {
    const themes = this.getPredefinedThemes();
    const themeConfig = themes[theme];
    
    if (themeConfig) {
      this.update(themeConfig);
    } else {
      console.warn(`Theme '${theme}' not found`);
    }
  }

  /**
   * Adds frame to QR code
   * @param frameStyle Frame style name
   */
  addFrame(frameStyle: FrameStyle): void {
    this.applyAdvancedStyle({ frameStyle });
  }

  /**
   * Adds center image to QR code
   * @param imagePath Path to center image
   * @param size Image size as percentage (0-0.5)
   */
  async addCenterImage(imagePath: string, size: number = 0.25): Promise<void> {
    this.applyAdvancedStyle({ 
      centerImage: imagePath, 
      centerImageSize: size 
    });
  }

  /**
   * Exports QR code in specified format
   * @param format Export format
   * @param options Export options
   * @returns Promise with exported data
   */
  async exportAs(format: ExportFormat, options?: ExportOptions): Promise<Buffer | string> {
    const rawData = await this.getRawData(format as FileExtension);
    
    if (!rawData) {
      throw new Error('Failed to generate QR code data');
    }

    if (format === 'svg' && typeof rawData === 'string') {
      return rawData;
    }

    return rawData instanceof Buffer ? rawData : Buffer.from(await (rawData as Blob).arrayBuffer());
  }

  /**
   * Converts QR code to base64
   * @param format Image format
   * @returns Promise with base64 string
   */
  async toBase64(format: string = 'png'): Promise<string> {
    const rawData = await this.getRawData(format as FileExtension);
    
    if (!rawData) {
      throw new Error('Failed to generate QR code data');
    }

    if (rawData instanceof Buffer) {
      return rawData.toString('base64');
    } else {
      const arrayBuffer = await (rawData as Blob).arrayBuffer();
      return Buffer.from(arrayBuffer).toString('base64');
    }
  }

  /**
   * Saves QR code to file
   * @param path File path
   * @param format File format
   */
  async saveToFile(path: string, format: string = 'png'): Promise<void> {
    if (typeof require === 'undefined') {
      throw new Error('File saving is only available in Node.js environment');
    }

    const rawData = await this.getRawData(format as FileExtension);
    if (!rawData) {
      throw new Error('Failed to generate QR code data');
    }

    const fs = require('fs').promises;
    const buffer = rawData instanceof Buffer ? rawData : Buffer.from(await (rawData as Blob).arrayBuffer());
    await fs.writeFile(path, buffer);
  }

  /**
   * Generates advanced styled QR code
   * @param extension File extension
   * @returns Promise with generated data
   */
  private async generateAdvancedQRCode(extension: FileExtension): Promise<Buffer> {
    const advancedOptions = {
      data: this.currentOptions.data || '',
      width: this.currentOptions.width,
      height: this.currentOptions.height,
      margin: this.currentOptions.margin,
      typeNumber: this.currentOptions.qrOptions?.typeNumber || 0,
      errorCorrectionLevel: this.currentOptions.qrOptions?.errorCorrectionLevel || 'M',
      styling: this.currentOptions.advancedStyling
    };

    return await this.advancedGenerator.generateBuffer(advancedOptions.data, advancedOptions);
  }

  /**
   * Merges options with defaults
   * @param options Partial options
   * @returns Complete options with defaults
   */
  private mergeWithDefaults(options: Partial<UnifiedOptions>): UnifiedOptions {
    return {
      width: 300,
      height: 300,
      margin: 0,
      type: 'canvas',
      data: '',
      qrOptions: {
        typeNumber: 0,
        mode: 'Byte',
        errorCorrectionLevel: 'M'
      },
      dotsOptions: {
        type: 'square',
        color: '#000000'
      },
      backgroundOptions: {
        color: '#FFFFFF'
      },
      features: {
        enablePix: false,
        enableAdvancedStyling: false,
        enableAnimations: false,
        strictMode: false
      },
      ...options
    };
  }

  /**
   * Gets predefined themes
   * @returns Record of theme configurations
   */
  private getPredefinedThemes(): Record<string, Partial<UnifiedOptions>> {
    return {
      'dark': {
        dotsOptions: { color: '#FFFFFF' },
        backgroundOptions: { color: '#000000' }
      },
      'neon': {
        dotsOptions: { color: '#00FF00' },
        backgroundOptions: { color: '#000000' },
        advancedStyling: { frameStyle: FrameStyle.SCAN_ME_NEON }
      },
      'professional': {
        dotsOptions: { color: '#2C3E50' },
        backgroundOptions: { color: '#ECF0F1' },
        advancedStyling: { frameStyle: FrameStyle.CLEAN }
      }
    };
  }

  /**
   * Gets current options
   * @returns Current unified options
   */
  getOptions(): UnifiedOptions {
    return { ...this.currentOptions };
  }

  /**
   * Gets QR code statistics
   * @returns Statistics object
   */
  getStats(): {
    dataLength: number;
    moduleCount: number;
    errorCorrectionLevel: string;
    isPix: boolean;
    hasAdvancedStyling: boolean;
  } {
    return {
      dataLength: this.currentOptions.data?.length || 0,
      moduleCount: 0, // Would be calculated from actual QR
      errorCorrectionLevel: this.currentOptions.qrOptions?.errorCorrectionLevel || 'M',
      isPix: !!this.currentOptions.pix,
      hasAdvancedStyling: !!this.currentOptions.advancedStyling
    };
  }
}
