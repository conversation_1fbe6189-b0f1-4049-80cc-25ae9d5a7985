# QR Code Styling - Refatoração SOLID

## Visão Geral

Este documento descreve a refatoração completa do projeto QR Code Styling, aplicando os princípios SOLID e integrando funcionalidades do QR-PIX. A refatoração visa melhorar a manutenibilidade, extensibilidade e testabilidade do código.

## Objetivos da Refatoração

1. **Aplicar Princípios SOLID**
   - Single Responsibility Principle (SRP)
   - Open/Closed Principle (OCP)
   - Liskov Substitution Principle (LSP)
   - Interface Segregation Principle (ISP)
   - Dependency Inversion Principle (DIP)

2. **Integrar Funcionalidades PIX**
   - Geração de BR-Code
   - Validação de chaves PIX
   - Parsing de códigos PIX

3. **Melhorar Arquitetura**
   - Separação de responsabilidades
   - Padrões de design apropriados
   - Código mais testável e manutenível

## Estrutura da Nova Arquitetura

### Core (Núcleo)
```
src/core/
├── interfaces/           # Interfaces principais
├── managers/            # Gerenciadores especializados
├── factories/           # Factories para criação de objetos
└── QRCodeStyling.ts    # Classe principal (legacy)
```

### Renderizadores
```
src/renderers/
├── base/               # Classe base para renderizadores
├── canvas/             # Renderizador Canvas
└── svg/                # Renderizador SVG
```

### Estilos
```
src/styles/
├── dots/               # Estratégias para pontos
├── processors/         # Processadores de estilo
└── engines/            # Engines de estilo
```

### PIX
```
src/pix/
├── generators/         # Geradores PIX
├── validators/         # Validadores PIX
├── parsers/           # Parsers BR-Code
└── composers/         # Compositores de imagem
```

### Processamento de Imagem
```
src/image/
└── processors/        # Processadores de imagem
```

## Principais Mudanças

### 1. Aplicação do Single Responsibility Principle (SRP)

**Antes:** Classe monolítica `QRCodeStyling` com múltiplas responsabilidades
**Depois:** Separação em managers especializados:

- `OptionsManager` - Gerencia opções e configurações
- `ElementManager` - Gerencia elementos DOM
- `QRDataManager` - Gerencia dados do QR Code
- `RenderingManager` - Gerencia renderização
- `ExportManager` - Gerencia exportação

### 2. Implementação do Strategy Pattern

**Dots (Pontos):**
- `SquareDotStrategy`
- `CircleDotStrategy`
- `RoundedDotStrategy`
- `ExtraRoundedDotStrategy`
- `ClassyDotStrategy`
- `ClassyRoundedDotStrategy`

### 3. Factory Pattern para Criação de Objetos

- `RendererFactory` - Cria renderizadores
- `StyleFactory` - Cria engines de estilo
- `ImageProcessorFactory` - Cria processadores de imagem
- `GeneratorFactory` - Cria geradores QR
- `DotStrategyFactory` - Cria estratégias de pontos

### 4. Integração PIX

**Componentes PIX:**
- `PixQRGenerator` - Gera QR codes PIX
- `BRCodeGenerator` - Gera BR-Codes
- `BRCodeParser` - Faz parsing de BR-Codes
- `PixValidators` - Valida chaves PIX

### 5. Renderizadores Especializados

**SVG Renderer Refatorado:**
- `SVGElementCreator` - Criação de elementos SVG
- `SVGStyleApplier` - Aplicação de estilos
- `SVGBackgroundDrawer` - Desenho de fundo
- `SVGDotDrawer` - Desenho de pontos
- `SVGCornerDrawer` - Desenho de cantos
- `SVGImageDrawer` - Desenho de imagens

## API Unificada

### Classe Principal
```typescript
import { UnifiedQRCode } from 'qr-code-styling';

// Uso básico
const qr = new UnifiedQRCode({
  data: "https://example.com",
  width: 300,
  height: 300
});

// PIX
const pixQR = new UnifiedQRCode({
  pix: {
    key: "<EMAIL>",
    nameReceiver: "João Silva",
    cityReceiver: "São Paulo",
    amount: 10.50
  }
});

// Estilo avançado
const styledQR = new UnifiedQRCode({
  data: "https://example.com",
  advancedStyling: {
    markerStyle: 'rounded',
    gradientColor: '#FF6B6B',
    frameStyle: 'tech'
  }
});
```

### Funções de Conveniência
```typescript
import { createQRCode, createPixQR, createStyledQR } from 'qr-code-styling';

// QR padrão
const standardQR = createQRCode('standard', { data: "Hello World" });

// QR PIX
const pixQR = createPixQR({
  key: "11999999999",
  nameReceiver: "Maria Silva",
  cityReceiver: "Rio de Janeiro"
});

// QR estilizado
const styledQR = createStyledQR("https://example.com", {
  markerStyle: 'star',
  frameStyle: 'creative'
});
```

## Compatibilidade

### Backward Compatibility
A refatoração mantém compatibilidade com a API existente:

```typescript
// Ainda funciona
import QRCodeStyling from 'qr-code-styling';
const qr = new QRCodeStyling({ data: "test" });
```

### Migration Helpers
```typescript
import { Migration } from 'qr-code-styling';

// Migrar de qr-code-styling v1.x
const newOptions = Migration.fromQRCodeStyling(oldOptions);

// Migrar de qr-pix v2.x
const newOptions = Migration.fromQRPix(oldPixOptions);
```

## Benefícios da Refatoração

### 1. Manutenibilidade
- Código mais organizado e modular
- Responsabilidades bem definidas
- Fácil localização de bugs

### 2. Extensibilidade
- Novos tipos de renderizadores facilmente adicionáveis
- Novas estratégias de estilo plugáveis
- Sistema de plugins preparado

### 3. Testabilidade
- Classes pequenas e focadas
- Dependências injetáveis
- Mocking facilitado

### 4. Performance
- Lazy loading de componentes
- Cache inteligente
- Processamento otimizado

### 5. Funcionalidades PIX
- Geração completa de BR-Codes
- Validação robusta de chaves PIX
- Parsing confiável de códigos

## Próximos Passos

1. **Testes Unitários**
   - Implementar testes para todos os componentes
   - Coverage de 90%+

2. **Documentação**
   - API documentation completa
   - Exemplos de uso
   - Guias de migração

3. **Performance**
   - Benchmarks
   - Otimizações específicas
   - Web Workers para processamento pesado

4. **Plugins**
   - Sistema de plugins funcional
   - Plugins da comunidade

## Conclusão

A refatoração transforma o projeto em uma arquitetura moderna, extensível e maintível, seguindo as melhores práticas de desenvolvimento de software. A integração com PIX adiciona valor significativo para o mercado brasileiro, enquanto a compatibilidade garante uma transição suave para os usuários existentes.
